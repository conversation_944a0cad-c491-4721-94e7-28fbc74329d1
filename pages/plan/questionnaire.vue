<template>
  <view class="bg">
    <view class="bgs">
      <image :src="imgUrl+'/qdkbm/newimage/fhpic/fh-bg11.png'" mode="widthFix"
             style="position: absolute;left:0;top:0;width:100%"></image>
    </view>
    <u-popup :round="8" mode="center" bgColor="transparent" :show="show">
      <view class="picbox"
            :style="{'background-image': 'url('+imgUrl+'/qdkbm/newimage/fhpic/pop-bg.png)'}">
        <view class="closeicon" @tap="close">
          <image :src="imgUrl+'/qdkbm/newimage/fhpic/fh-cancelbtn.png'"></image>
        </view>

        <view class="myboxs">
          <view class="tit2">报告生成中</view>
          <view class="cont">
            <text>可能需要几分钟，请耐心等待\n也可去使用其他功能,点击行为记录可进行查看</text>

          </view>
          <view class="jiazaizhong">
            <image :src="imgUrl+'/qdkbm/newimage/fhui/loadingpic2.gif'"
                   style="width:500rpx;height:60rpx"></image>
          </view>
          <view class="btn">
            <button @tap="goHome">返回首页</button>
          </view>
        </view>
      </view>

    </u-popup>
    <view class="head" style="background:none" :style="'padding-top:' + (titleTop+10) + 'px'">
      <view class="header1" style="padding-bottom: 15rpx;">
        <view class="left" @tap="back" style="padding-bottom: 10rpx;">
          <view class="itemList">
            <view class="item">
              <button style="background-color: rgba(255, 255, 255, 0.3); border-radius: 50%; width: 60rpx; height: 60rpx; display: flex; align-items: center; justify-content: center;">
                <image style="width:24rpx"
                       :src="imgUrl+'/qdkbm/newimage/fhui/back-light.png'">
                </image>
              </button>
            </view>
          </view>
        </view>
        <view class="text" style="font-size: 32rpx; padding-bottom: 10rpx;">规划报告</view>
      </view>
    </view>
    <view class="container">
      <!-- 已选专业展示区域已移动到搜索框下方 -->
      <view class="question-container">
        <view class="question-header" v-if="questions.length > 0 && currentQuestion >= 0 && currentQuestion < questions.length && questions[currentQuestion].id !== 8">
          <text class="question-number">{{ getDisplayQuestionNumber() }}</text>
          <text class="question-title">{{ questions[currentQuestion].content }}</text>
        </view>
        <view class="question-header" v-else-if="questions.length === 0 || currentQuestion < 0 || currentQuestion >= questions.length">
          <text class="question-number"></text>
          <text class="question-title">加载中...</text>
        </view>
        <view class="question-content" style="padding-bottom: 20rpx;">
          <view v-if="questions.length>0 && currentQuestion >= 0 && currentQuestion < questions.length && questions[currentQuestion].id ===1" class="cityList">
            <view class="myitem"
                  :class="[answers[questions[currentQuestion].id]==item.id?'active':'', 'disabled-province']"
                  v-for="(item,index) in questions[currentQuestion].questionChoiceDoS" v-if="!item.hidden"
                  :key="index">
              {{ item.choiceContent }}
            </view>
            <view class="province-note" style="font-size: 24rpx; color: #999; margin-top: 20rpx; text-align: center;">
              省份信息来自您的用户资料，不可在此修改
            </view>
          </view>

          <view v-if="questions.length>0 && currentQuestion >= 0 && currentQuestion < questions.length && questions[currentQuestion].id ===2" class="sexList">
            <view class="myitem" v-if="!item.hidden" @tap="clickSex(item.id,questions[currentQuestion].id)"
                  :class="answers[questions[currentQuestion].id]==item.id?'active':''"
                  v-for="(item,index) in questions[currentQuestion].questionChoiceDoS" :key="index">
              <view class="img">
                <view class="iconchecked" v-if="answers[questions[currentQuestion].id]==item.id"
                      style="position: absolute;right:0;top:10rpx">
                  <image style="width:50rpx;height:50rpx;"
                         :src="imgUrl+'/qdkbm/newimage/fhpic/fh-duihao.png'"></image>
                </view>
                <image
                    :src="item.choiceContent=='我是男生'?answers[questions[currentQuestion].id]==item.id?imgUrl+'/qdkbm/newimage/fhpic/fh-boy-cur.png':imgUrl+'/qdkbm/newimage/fhpic/fh-boy.png':answers[questions[currentQuestion].id]===item?imgUrl+'/qdkbm/newimage/fhpic/fh-girl-cur.png':imgUrl+'/qdkbm/newimage/fhpic/fh-girl.png'">
                </image>
              </view>
              <view class="text">{{ item.choiceContent }}</view>
            </view>

          </view>

          <!-- 单选题 -->
          <block
              v-if="questions.length>0 && currentQuestion >= 0 && currentQuestion < questions.length && questions[currentQuestion].type === 1   && questions[currentQuestion].id !== 1  && questions[currentQuestion].id !== 2 && questions[currentQuestion].id !== 8">
            <view class="radio-list">
              <view class="item" v-if="!item.hidden" :data-id="item.id"
                    :data-question-id="questions[currentQuestion].id"
                    :class="answers[questions[currentQuestion].id]==item.id?'cur':''"
                    v-for="(item,index) in questions[currentQuestion].questionChoiceDoS" :key="index"
                    @tap="handleSelect">

                <view class="icon">
                  <image
                      :src="answers[questions[currentQuestion].id]==item.id?imgUrl+'/qdkbm/newimage/fh-notchecked.png':imgUrl+'/qdkbm/newimage/fh-checked.png'">
                  </image>
                </view>
                <view class="text">{{ item.choiceContent }}</view>

              </view>

            </view>
          </block>

          <!-- 特殊处理：ID为8、9、10的题目在同一页面显示 -->
          <block v-if="questions.length>0 && currentQuestion >= 0 && currentQuestion < questions.length && questions[currentQuestion].id === 8">
            <view class="multi-questions-container">
              <!-- 第一个问题（ID=8） -->
              <view class="multi-question-item">
                <view class="multi-question-header">
                  <text class="multi-question-number">6</text>
                  <text class="multi-question-title">{{ getQuestionById(8).content }}</text>
                </view>
                <view class="radio-list">
                  <view class="item" v-if="!item.hidden" :data-id="item.id"
                        :data-question-id="8"
                        :class="answers[8]==item.id?'cur':''"
                        v-for="(item,index) in getQuestionById(8).questionChoiceDoS" :key="index"
                        @tap="handleSelect">
                    <view class="icon">
                      <image
                          :src="answers[8]==item.id?imgUrl+'/qdkbm/newimage/fh-notchecked.png':imgUrl+'/qdkbm/newimage/fh-checked.png'">
                      </image>
                    </view>
                    <view class="text">{{ item.choiceContent }}</view>
                  </view>
                </view>
              </view>

              <!-- 第二个问题（ID=9） -->
              <view class="multi-question-item">
                <view class="multi-question-header">
                  <text class="multi-question-number">7</text>
                  <text class="multi-question-title">{{ getQuestionById(9).content }}</text>
                </view>
                <view class="radio-list">
                  <view class="item" v-if="!item.hidden" :data-id="item.id"
                        :data-question-id="9"
                        :class="answers[9]==item.id?'cur':''"
                        v-for="(item,index) in getQuestionById(9).questionChoiceDoS" :key="index"
                        @tap="handleSelect">
                    <view class="icon">
                      <image
                          :src="answers[9]==item.id?imgUrl+'/qdkbm/newimage/fh-notchecked.png':imgUrl+'/qdkbm/newimage/fh-checked.png'">
                      </image>
                    </view>
                    <view class="text">{{ item.choiceContent }}</view>
                  </view>
                </view>
              </view>

              <!-- 第三个问题（ID=10） -->
              <view class="multi-question-item">
                <view class="multi-question-header">
                  <text class="multi-question-number">8</text>
                  <text class="multi-question-title">{{ getQuestionById(10).content }}</text>
                </view>
                <view class="radio-list">
                  <view class="item" v-if="!item.hidden" :data-id="item.id"
                        :data-question-id="10"
                        :class="answers[10]==item.id?'cur':''"
                        v-for="(item,index) in getQuestionById(10).questionChoiceDoS" :key="index"
                        @tap="handleSelect">
                    <view class="icon">
                      <image
                          :src="answers[10]==item.id?imgUrl+'/qdkbm/newimage/fh-notchecked.png':imgUrl+'/qdkbm/newimage/fh-checked.png'">
                      </image>
                    </view>
                    <view class="text">{{ item.choiceContent }}</view>
                  </view>
                </view>
              </view>
            </view>
          </block>


          <!-- 多选题 -->

          <block
              v-if="questions.length>0 && currentQuestion >= 0 && currentQuestion < questions.length && questions[currentQuestion].type === 2 && questions[currentQuestion].id !== 7">
            <view class="radio-list">
              <!-- 如果是选科题(ID为3)，显示提示信息 -->
              <view v-if="questions[currentQuestion].id === 3" class="subject-selection-info">
                <view class="selected-subjects-title">您的选科情况：</view>
                <view class="selected-subjects-list">
                  <!-- 显示选中的选项 -->
                  <view v-for="(item,index) in questions[currentQuestion].questionChoiceDoS.filter(item => item.checked)" :key="index"
                        class="subject-choice-item selected">
                    <view class="icon">
                      <image :src="imgUrl+'/qdkbm/newimage/fh-notchecked.png'" style="width:40rpx;height:40rpx;"></image>
                    </view>
                    <view class="text">{{ item.choiceContent }}</view>
                  </view>

                  <!-- 如果没有选中的选项，则显示提示信息 -->
                  <view v-if="!questions[currentQuestion].questionChoiceDoS.some(item => item.checked)" class="no-subjects">
                    暂无选科数据，请联系管理员更新您的选科信息
                  </view>
                </view>
                <view class="subject-selection-note">选科信息来自您的用户资料，不可在此修改</view>

<!--                &lt;!&ndash; 添加一个检查选科状态的按钮 &ndash;&gt;-->
<!--                <view @tap="checkSubjectSelectionStatus" style="margin-top: 20rpx; padding: 10rpx 20rpx; background-color: #f0f0f0; border-radius: 8rpx; display: inline-block;">-->
<!--                  刷新选科状态-->
<!--                </view>-->

<!--                &lt;!&ndash; 打印选中的选项，用于调试 &ndash;&gt;-->
<!--                <view class="debug-info" style="margin-top: 20rpx; font-size: 24rpx; color: #999; text-align: left;">-->
<!--                  <view>选中的选项数量：{{ questions[currentQuestion].questionChoiceDoS.filter(item => item.checked).length }}</view>-->
<!--                  <view>选中的选项：{{ questions[currentQuestion].questionChoiceDoS.filter(item => item.checked).map(item => item.choiceContent).join(', ') }}</view>-->
<!--                  <view>用户信息中的选科：{{ userInfo && userInfo.secondSubject ? userInfo.secondSubject : '无' }}</view>-->
<!--                  <view>fkList: {{ fkList }}</view>-->
<!--                  <view>答案: {{ answers[3] }}</view>-->
<!--                </view>-->
              </view>

              <!-- 其他多选题正常显示可编辑选项 -->
              <view v-else class="item" v-if="!item.hidden" :class="item.checked?'cur':''"
                    v-for="(item,index) in questions[currentQuestion].questionChoiceDoS" :key="index"
                    @tap="handleMultiSelect(item,questions[currentQuestion].id,questions[currentQuestion].questionChoiceDoS)">

                <view class="icon">
                  <image
                      :src="item.checked?imgUrl+'/qdkbm/newimage/fh-notchecked.png':imgUrl+'/qdkbm/newimage/fh-checked.png'">
                  </image>
                </view>
                <view class="text">{{ item.choiceContent }}</view>

              </view>


            </view>
          </block>


          <!-- 多选题 -->
          <view v-if="questions.length>0 && currentQuestion >= 0 && currentQuestion < questions.length && questions[currentQuestion].id === 7">
            <view class="test tree-select-container">
              <!-- 搜索框 -->
              <view class="search-box">
                <view class="search-input">
                  <input type="text" v-model="searchKeyword" @input="searchMajors" placeholder="请输入专业名称" placeholder-class="placeholder"/>
                  <view v-if="searchKeyword" class="clear-icon" @tap="clearSearch">×</view>
                </view>
              </view>

              <!-- 已选专业展示区域 -->
              <view v-if="yxzy_ids && yxzy_ids.length > 0" class="selected-majors-container" key="selected-majors">
                <view class="selected-majors-list">
                  <view class="selected-majors-title">已选：</view>
                  <view v-for="(majorId, index) in yxzy_ids" :key="index" class="selected-major-item" @tap="directSelectMajorById(majorId)">
                    <text>{{ getMajorNameById(majorId) }}</text>
                    <text class="remove-major">×</text>
                  </view>
                </view>
              </view>

              <!-- 搜索模式提示 -->
              <view v-if="isSearchMode" class="search-mode-tip">
                <view class="tip-content">
                  <text>搜索结果仅包含{{ currentEducationLevel }}专业</text>
                  <text class="clear-search" @tap="clearSearch">清除搜索</text>
                </view>
              </view>

              <!-- 标签页 -->
              <view class="tab-bar" v-if="!isSearchMode">
                <view v-if="educationLevelCounts['本科（普通教育）'] > 0"
                      class="tab-item"
                      :class="{active: currentEducationLevel === '本科（普通教育）'}"
                      @tap="changeEducationLevel('本科（普通教育）')">
                  本科(普通)
                </view>
                <view v-if="educationLevelCounts['本科（职业教育）'] > 0"
                      class="tab-item"
                      :class="{active: currentEducationLevel === '本科（职业教育）'}"
                      @tap="changeEducationLevel('本科（职业教育）')">
                  本科(职业)
                </view>
                <view v-if="educationLevelCounts['高职（专科）'] > 0"
                      class="tab-item"
                      :class="{active: currentEducationLevel === '高职（专科）'}"
                      @tap="changeEducationLevel('高职（专科）')">
                  专科(高职)
                </view>
                <view v-if="!hasAnyEducationLevel" class="tab-item no-data">
                  暂无专业数据
                </view>
              </view>

              <!-- 新的专业展示布局 -->
              <view class="major-display-container">
                <!-- 左侧分类导航 -->
                <view class="category-nav">
                  <view
                    v-for="(category, index) in items"
                    :key="category.id"
                    class="nav-item"
                    :class="{ 'active': selectedCategoryIndex === index }"
                    @click="selectCategory(index)">
                    {{ category.name.replace('类', '') }}
                  </view>
                </view>

                <!-- 右侧专业列表 -->
                <view class="major-list-container">
                  <!-- 分类标题 -->
                  <view v-if="selectedCategory" class="category-header" @click="toggleCategoryExpand">
                    <text class="category-title">{{ selectedCategory.name }}</text>
                    <text class="major-count">{{ getTotalMajorCount() }}个专业</text>
                    <text class="expand-icon" :class="{ 'expanded': isCategoryExpanded }">^</text>
                  </view>

                  <!-- 专业列表 -->
                  <scroll-view v-if="isCategoryExpanded" class="major-scroll" scroll-y="true">
                    <view v-for="major in getAllMajorsInCategory()" :key="major.id"
                          class="major-item-new"
                          :class="{ 'selected': isIdSelected(major.id), 'recommended': major.isRecommended }"
                          @click="directSelectMajor(major)">

                      <!-- 推荐专业火焰特效 -->
                      <view v-if="major.isRecommended" class="flame-effect-new">
                        <view class="flame flame1">🔥</view>
                        <view class="flame flame2">🔥</view>
                        <view class="flame flame3">🔥</view>
                      </view>

                      <!-- 推荐标签 -->
                      <view v-if="major.isRecommended" class="recommended-badge">
                        推荐
                      </view>

                      <!-- 专业名称 -->
                      <view class="major-name-new" :class="{ 'has-badge': major.isRecommended }">{{ major.name }}</view>

                      <!-- 专业详细信息 -->
                      <view class="major-details">
                        <!-- 第一行：年限和学位 -->
                        <view class="detail-row">
                          <view class="detail-item">
                            <text class="detail-label">年限</text>
                            <text class="detail-value">{{ getMajorDuration(major) }}</text>
                          </view>
                          <view class="detail-item" v-if="!isZhuanke(major)">
                            <text class="detail-label">学位</text>
                            <text class="detail-value">{{ getMajorDegree(major) }}</text>
                          </view>
                        </view>

                        <!-- 第二行：代码和规模 -->
                        <view class="detail-row">
                          <view class="detail-item">
                            <text class="detail-label">代码</text>
                            <text class="detail-value">{{ major.code || '090502' }}</text>
                          </view>
                          <view class="detail-item" v-if="major.graduateScale">
                            <text class="detail-label">规模</text>
                            <text class="detail-value">{{ major.graduateScale }}</text>
                          </view>
                        </view>

                        <!-- 第三行：男女比例（如果有数据） -->
                        <view class="detail-row gender-ratio-row" v-if="isValidMaleFemaleRatio(major.maleFemaleRatio)">
                          <view class="gender-ratio-container">
                            <text class="detail-label">男女比例</text>
                            <view class="gender-ratio-bar">
                              <view class="male-bar" :style="{ width: getMaleRatioPercent(major.maleFemaleRatio) + '%' }"></view>
                              <view class="female-bar" :style="{ width: getFemaleRatioPercent(major.maleFemaleRatio) + '%' }"></view>
                            </view>
                            <view class="ratio-text">
                              <text class="male-text">{{ getMaleRatioPercent(major.maleFemaleRatio) || 50 }}%</text>
                              <text class="female-text">{{ getFemaleRatioPercent(major.maleFemaleRatio) || 50 }}%</text>
                            </view>
                          </view>
                        </view>

                        <!-- 就业方向 -->
                        <view v-if="major.careerDirection" class="career-direction-row">
                          <text class="detail-label">就业方向：</text>
                          <text class="career-direction-text">{{ major.careerDirection }}</text>
                        </view>
                      </view>

                      <!-- 选中状态指示器 -->
                      <view v-if="isIdSelected(major.id)" class="selected-indicator">
                        <image :src="imgUrl+'/qdkbm/newimage/fhpic/fh-duihao.png'" class="check-icon"></image>
                      </view>
                    </view>
                  </scroll-view>
                </view>
              </view>
            </view>

            <!-- <view class="dx-part" style="display: none;">
							<view class="dx-left">
								<view class="item" :class="item.checked?'active':''" @tap="changeLeftNav(item)"
									v-for="(item,index) in leftNavList" :key="index">{{item.name}}
									<view class="zy-list">
										<view class="myitem" :class="myitem.checked?'active':''"
											@tap="changemyItem(myitem)" v-for="(myitem,myindex) in item.children"
											:key="myindex">
											{{myitem.name}}
										</view>
									</view>
								</view>
							</view>

						</view> -->
            <!-- <checkbox-group :data-question-id="questions[currentQuestion].id" @change="handleMultiSelect">
							<label class="option-item"
								v-for="(item,index) in questions[currentQuestion].questionChoiceDoS" :key="index">
								<checkbox :value="item.id" />
								<text>{{item.choiceContent}}</text>
							</label>
						</checkbox-group> -->
          </view>


          <!-- 输入框 - 高考总分（不可修改） -->
          <view
              v-if="questions.length>0 && currentQuestion >= 0 && currentQuestion < questions.length && questions[currentQuestion].type === 3 && questions[currentQuestion].id === 5"
              style="font-size:28rpx;color:#333;display: flex;flex-direction: row;align-items: center;">
            <view class="input-field-readonly"
                  style="font-size:32rpx;color:#FB6B3D;width: 160rpx;text-align:center;margin-right:20rpx;background:#FFF0EB;border-color:#FB6B3D;height:80rpx;line-height:80rpx;border-radius:8rpx;padding:0 20rpx;">
              {{ total }}
            </view> 分
            <view style="margin-left:20rpx;font-size:24rpx;color:#999;">(总分从用户信息中获取，不可修改)</view>
          </view>


          <view
              v-if="questions.length>0 && currentQuestion >= 0 && currentQuestion < questions.length && questions[currentQuestion].type === 3 && questions[currentQuestion].id === 13"
              style="font-size:28rpx;color:#333;display: flex;flex-direction: row;align-items: center;">
            <input class="input-field"
                   style="width:100%;font-size:32rpx;color:#333;background:#f6f6f6;border-color:#f6f6f6;"
                   :data-question-id="questions[currentQuestion].id"
                   :value="answers[questions[currentQuestion].id]"
                   @input="handleInput"
                   placeholder="例如:税务局"
                   maxlength="15"/>
          </view>
          <!-- 科目分数 -->
          <view
              v-if="questions.length>0 && currentQuestion >= 0 && currentQuestion < questions.length && questions[currentQuestion].id === 6"
              class="subjects-container">
            <!-- 科目提示信息 -->
            <view v-if="getSelectedSubjects().length === 0" style="color:#FB6B3D;padding:20rpx;font-size:28rpx;">
              您尚未选择任何科目，请返回上一题选择您要录入分数的科目
            </view>
            <!-- 显示用户选择的科目 -->
<!--            <view v-if="getSelectedSubjects().length > 0" class="subject-hint" style="padding:10rpx 20rpx;font-size:28rpx;color:#666;margin-bottom:20rpx;">-->
<!--              请录入您选择的科目分数-->
<!--            </view>-->
            <view class="subject-item" v-for="subject in getSelectedSubjects()" :key="subject.id">
              <text class="subject-name">{{ subject.name }}：</text>
              <input class="subject-score"
                     style="height:70rpx;font-size:28rpx;line-height:70rpx;padding:0;padding-left:20rpx;background:#f6f6f6;border-color:#f6f6f6;margin-right:20rpx;"
                     :data-subject-id="subject.id"
                     :value="subjectScores[subject.id] || ''"
                     @input="(e) => handleSubjectScoreNew(e, subject.id, subject.name)"
                     type="digit" placeholder="请输入" maxlength="3"/>
              <view class="unit">分</view>
            </view>
            <view class="total" style="padding:0 20rpx;font-size:28rpx;align-items: center;width:100%;font-weight:bold;color:#FB6B3D;">
              学生总分：{{ total || 0 }} 分
            </view>
            <!-- 已录入总分提示 -->
            <view class="total-summary" style="padding:0 20rpx;font-size:28rpx;align-items: center;width:100%;margin-top:10rpx;">
              <text>已录入总分：<text style="color:#FB6B3D;font-weight:bold;">{{ getCurrentSum() }}</text> 分</text>
            </view>
            <!-- 总分差值提示 -->
            <view class="total-difference" style="padding:0 20rpx;font-size:28rpx;align-items: center;width:100%;margin-top:10rpx;">
              <text v-if="getDifference() > 0">还差 <text style="color:#FB6B3D;font-weight:bold;">{{ getDifference() }}</text> 分</text>
              <text v-else-if="getDifference() < 0">超出 <text style="color:#FF0000;font-weight:bold;">{{ Math.abs(getDifference()) }}</text> 分</text>
              <text v-else style="color:#00AA00;font-weight:bold;">✓ 分数已录入完成</text>
            </view>
            <view v-if="total" style="padding:0 20rpx;font-size:24rpx;color:#999;">
              (注意：请录入您的各科分数，以便输入更精确的报告)
            </view>
          </view>
        </view>
      </view>
      <view class="button-container">
        <button v-if="currentQuestion > 0" class="nav-button prev-button" v-prevent-click="500" @tap="prevQuestion">上一题</button>
        <button v-if="questions.length > 0 && currentQuestion >= 0 && currentQuestion < questions.length - 1" class="nav-button next-button"
                :class="questions.length > 0 && currentQuestion >= 0 && currentQuestion < questions.length ? (questions[currentQuestion].id===3?(answers[3] && answers[3].length===3)?'':'disabled':questions[currentQuestion].id===5?'':questions[currentQuestion].id===6?(getCurrentSum() > 0 && Math.abs(getCurrentSum() - total) < 0.1)?'':'disabled':questions[currentQuestion].id===7?(yxzy_ids && yxzy_ids.length>0)?'':'disabled':questions[currentQuestion].id==13?'':isCurrentQuestionAnswered || answers[questions[currentQuestion].id]?'':'disabled') : ''"
                v-prevent-click="500"
                @tap="nextQuestion"
                :disabled="questions.length > 0 && currentQuestion >= 0 && currentQuestion < questions.length ? (questions[currentQuestion].id===3?(answers[3] && answers[3].length===3)?false:true:questions[currentQuestion].id===5?false:questions[currentQuestion].id===6?(getCurrentSum() > 0 && Math.abs(getCurrentSum() - total) < 0.1)?false:true:questions[currentQuestion].id===7?(yxzy_ids && yxzy_ids.length>0)?false:true:questions[currentQuestion].id==13?false:!isCurrentQuestionAnswered && !answers[questions[currentQuestion].id]) : true">
          下一题
        </button>
        <button v-if="questions && questions.length > 0 && currentQuestion >= 0 && currentQuestion === questions.length - 1"
                :disabled="questions && questions.length > 0 && currentQuestion >= 0 && currentQuestion < questions.length ? (!isCurrentQuestionAnswered&&!answers[questions[currentQuestion].id]) : true"
                v-prevent-click="2000"
                class="submit-button" @tap="submitQuestionnaire">提交
        </button>
      </view>
    </view>
  </view>
</template>
<script>
import wyhTreeSelect2 from '@/components/wyh-tree-select/wyh-tree-select2.vue'

export default {
  components: {
    wyhTreeSelect2
  },
  data() {
    return {
      answerNo: uni.getStorageSync('planQuestionnaireAnswerNo') || -1,
      // 控制是否按教育层次筛选专业的标志，默认为true（按教育层次筛选专业）
      filterByEducationLevel: true,
      // 是否处于搜索模式
      isSearchMode: false,
      show: false,
      isSubmitting: false, // 防止重复提交
      imgUrl: this.$base.uploadImgUrl,
      items: [], // 专业选择树
      height: 0,
      total: 0,
      questions: [],
      questionTypes: [1, 2, 3],
      currentQuestion: 0,
      questionBackup: {},
      answers: {},
      subjects: [],
      // 添加一个独立的科目分数存储对象，彻底避免互相影响
      subjectScores: {},
      selectedChoices: [],
      workId: "",
      majorNameCache: {},
      showSubmit: false,
      isCurrentQuestionAnswered: false,
      // 分数验证相关
      scoreSumError: false,
      // 科目选择相关
      fkList: [],
      // 分数科目列表
      fsList: [],
      // 搜索相关
      searchKeyword: '',
      searchResults: [],
      originalItems: null,
      // 当前教育层次
      currentEducationLevel: '本科（普通教育）',
      // 已选专业ID列表
      yxzy_ids: [],
      // 答案编号，用于跟踪用户的回答
      // answerNo: -1, // 已移到顶部
      // 存储用户推荐专业数据
      recommendedMajorsData: null,
      // 存储每个教育层次的专业数量
      educationLevelCounts: {
        '本科（普通教育）': 0,
        '本科（职业教育）': 0,
        '高职（专科）': 0
      },
      // 新的专业展示相关数据
      selectedCategoryIndex: 0,
      selectedCategory: null,
      isCategoryExpanded: true,
      // 防止重复点击的标志
      isProcessingSelection: false,
      ver: 1
    }
  },
  onLoad(options) {
    // 初始化状态
    uni.getSystemInfo({
      success: (res) => {
        this.titleTop = res.statusBarHeight
      }
    })

    this.workId = options.workId || 0
    if(options.ver){
      this.ver=options.ver
    }

    // 从本地存储读取answerNo，如果有的话
    const savedAnswerNo = uni.getStorageSync('planQuestionnaireAnswerNo');
    if (savedAnswerNo && savedAnswerNo !== -1 && savedAnswerNo !== '-1') {
      this.answerNo = savedAnswerNo;
      console.log('从本地存储恢复answerNo:', savedAnswerNo);
    }

    // 优先获取用户信息
    this.getUserInfo();
    // 获取问题列表
    this.getquestionList()
  },
  // 页面加载完成后调用
  onReady() {
    // 延时执行，确保页面已经加载完成
    setTimeout(() => {
      // 检查选科题的状态
      this.checkSubjectSelectionStatus();
    }, 1000);
  },

  // 添加watch属性监听当前题目变化
  watch: {
    currentQuestion(newVal, oldVal) {
      // 当切换到选科题时，检查选科题的状态
      if (this.questions[newVal] && this.questions[newVal].id === 3) {
        // console.log('切换到选科题，检查选科题的状态');
        this.$nextTick(() => {
          this.checkSubjectSelectionStatus();
        });
      }
      // 防止循环跳转
      if (this._skipWatchHandler) {
        // console.log('跳过watch处理：内部导航进行中');
        return;
      }

      // 打印当前题目信息以进行调试
      // console.log(`当前题目变化: 从 ${oldVal} 到 ${newVal}`);

      // 检查是否是用户手动操作导致的题目变化
      if (this._isNavigating) {
        // console.log('用户手动导航中，不触发自动跳转');
        return;
      }

      if (this.questions && this.questions.length > 0 && newVal >= 0 && newVal < this.questions.length) {
        // console.log(`当前题目内容: ${this.questions[newVal].content}, ID: ${this.questions[newVal].id}`);

        // 如果当前题目是科目分数题，确保总分是最新的并初始化输入框
        if (this.questions[newVal].id === 6) {
          // console.log('检测到分数题，当前值:', this.total);
          // console.log('当前 subjectScores:', this.subjectScores);
          // console.log('当前 answers[6]:', this.answers[6]);

          // 使用新的加载分数数据方法
          this.loadScoreData();

          // 同步answers[6]中的分数到subjectScores对象，以便显示在输入框中
          this.syncAnswersToSubjectScores();

          // 确保我们有最新的用户信息和总分
          if (!this.userInfo || this.total === 0) {
            this.getUserInfo();
          }

          // 检查是否已有分数数据
          const hasScoreData = this.subjectScores && Object.keys(this.subjectScores).length > 0 &&
                              Object.values(this.subjectScores).some(score => score && score.trim() !== '');

          // 如果已有分数数据，则不重新初始化
          if (hasScoreData) {
            // console.log('已有分数数据，不重新初始化');
            return;
          }

          // 检查第3题选择的科目，生成分数题的subjects
          if (this.fkList && this.fkList.length > 0) {
            // 生成选中科目列表
            const subjects = this.fkList.map(subject => {
              const questionChoices = this.questions.find(q => q.id === 3)?.questionChoiceDoS || [];
              const name = this.LabelFn(subject, questionChoices);
              // console.log('分数题初始化科目:', name);
              return {
                name: name,
                score: ''
              };
            });

            // 初始化基础科目
            let basicSubjects = [
              { name: '语文', score: '' },
              { name: '数学', score: '' },
              { name: '外语', score: '' }
            ];

            // 注意：第2题是性别选择，不是文理选择
            // 文理选择应该是第1题的答案
            let trackName = '';
            // 获取第1题的选择（文理选择）
            if (this.answers[1]) {
              const provinceQuestion = this.questions.find(q => q.id === 1);
              if (provinceQuestion) {
                const choice = provinceQuestion.questionChoiceDoS.find(choice => choice.id === this.answers[1]);
                if (choice) {
                  // 根据选择确定文理方向，先默认为'文科'
                  trackName = '文科';
                  // console.log('第1题选择的省份:', choice.choiceContent);
                }
              }
            }

            // 合并所有科目 - 用必修科目和选修科目
            let allSubjects = [
              ...basicSubjects,  // 语文、数学、外语
              ...subjects       // 选修科目（物理、化学等）
            ].filter(subject => subject && subject.name); // 过滤空科目

            // 设置科目列表
            this.subjects = allSubjects;
            // console.log('生成的科目数组:', allSubjects);

            // 确保第6题的subjects已设置
            this.$set(this.questions[6], 'subjects', allSubjects);

            // 初始化第6题的答案数组
            this.$set(this.answers, 6, allSubjects.map(subject => ({
              name: subject.name,
              score: ''
            })));

            // 初始化 subjectScores 对象
            allSubjects.forEach(subject => {
              // 根据科目名称确定ID
              let subjectId = '';
              switch(subject.name) {
                case '语文': subjectId = 'chinese'; break;
                case '数学': subjectId = 'math'; break;
                case '英语': subjectId = 'english'; break;
                case '外语': subjectId = 'english'; break;
                case '物理': subjectId = 'physics'; break;
                case '化学': subjectId = 'chemistry'; break;
                case '生物': subjectId = 'biology'; break;
                case '政治': subjectId = 'politics'; break;
                case '历史': subjectId = 'history'; break;
                case '地理': subjectId = 'geography'; break;
                case '技术': subjectId = 'tech'; break;
                default: subjectId = subject.name.toLowerCase().replace(/\s+/g, '_');
              }

              // 只有当该科目在 subjectScores 中不存在时才初始化
              if (!this.subjectScores[subjectId]) {
                this.$set(this.subjectScores, subjectId, '');
              }
            });
          } else {
            // console.log('未选择科目或fkList为空，无法初始化分数题');
          }
        }

        // ID为12的题目已经被过滤掉，不再需要跳过逻辑
      }
    }
  },
  onShow() {
    // 如果页面重新显示，确保用户信息已加载
    if (!this.userInfo) {
      this.getUserInfo()
    }

    // 每次页面显示时都检查并加载分数
    this.$nextTick(() => {
      this.loadScoreData();
    });
  },
  onPageScroll(e) {
  },
  mounted() {
  },
  onReady() {
    let res = uni.getMenuButtonBoundingClientRect();
    this.titleTop = res.top
    const query = uni.createSelectorQuery().in(this)
    query.selectAll('.head').boundingClientRect(data => {
      // console.log(data)
      this.height = data[0].height + this.titleTop - 4
      // console.log(this.height)
    }).exec()

    // 确保subjectScores对象已初始化
    if (!this.subjectScores) {
      this.subjectScores = {};
    }

    // 如果有answers[6]数据，同步到subjectScores
    this.syncAnswersToSubjectScores();
  },
  methods: {
    // 同步answers[6]到subjectScores对象的工具方法
    syncAnswersToSubjectScores() {
      // console.log('开始尝试同步answers[6]到subjectScores对象');
      // console.log('当前answers[6]:', JSON.stringify(this.answers[6]));

      // 强制确保分数能够在界面显示
      if (Array.isArray(this.answers[6]) && this.answers[6].length > 0) {
        let hasScoreData = false;

        // 遍历answers[6]中的每个科目
        this.answers[6].forEach(subject => {
          if (subject && subject.name) {
            // 根据科目名称确定ID
            let subjectId = '';
            switch(subject.name) {
              case '语文': subjectId = 'chinese'; break;
              case '数学': subjectId = 'math'; break;
              case '英语': subjectId = 'english'; break;
              case '外语': subjectId = 'english'; break;
              case '物理': subjectId = 'physics'; break;
              case '化学': subjectId = 'chemistry'; break;
              case '生物': subjectId = 'biology'; break;
              case '政治': subjectId = 'politics'; break;
              case '历史': subjectId = 'history'; break;
              case '地理': subjectId = 'geography'; break;
              case '技术': subjectId = 'tech'; break;
              default: subjectId = subject.name.toLowerCase().replace(/\s+/g, '_');
            }

            // 即使分数是null也要尝试提取实际值
            let scoreValue = '';
            if (subject.score !== undefined && subject.score !== null) {
              scoreValue = String(subject.score);
              if (scoreValue !== '' && scoreValue !== 'null') {
                hasScoreData = true;
              }
            }

            if (subjectId) {
              // console.log(`同步分数: ${subject.name} (${subjectId}) = ${scoreValue}`);
              this.$set(this.subjectScores, subjectId, scoreValue);
            }
          }
        });

        if (hasScoreData) {
          // console.log('成功同步分数数据到界面');
          // 更新fsList数组
          this.updateFsList();
          // 强制更新界面
          this.$forceUpdate();
        } else {
          // console.log('未找到有效的分数数据');
        }
        // console.log('同步后的subjectScores:', this.subjectScores);
      } else {
        // console.log('answers[6]不存在或为空数组');
      }
    },

    // 计算显示的问题序号，跳过第12题后保持序号连续
    // 直接从answers[6]数组获取科目分数，用于输入框显示
    getSubjectScoreValue(subjectName, subjectId) {
      // 首先检查subjectScores中是否有值
      if (this.subjectScores[subjectId] && this.subjectScores[subjectId] !== '') {
        return this.subjectScores[subjectId];
      }

      // 如果没有，则从answers[6]数组中获取
      if (Array.isArray(this.answers[6])) {
        for (let i = 0; i < this.answers[6].length; i++) {
          const subject = this.answers[6][i];
          if (subject && subject.name === subjectName && subject.score !== undefined && subject.score !== null) {
            // 找到对应的科目，保存到subjectScores中
            const scoreValue = String(subject.score);
            if (scoreValue !== '' && scoreValue !== 'null') {
              // console.log(`从answers[6]获取分数: ${subjectName} = ${scoreValue}`);
              // 更新到subjectScores
              this.$set(this.subjectScores, subjectId, scoreValue);
              return scoreValue;
            }
          }
        }
      }

      // 如果都没有找到，返回空字符串
      return '';
    },

    // 加载分数数据的方法，在页面显示和题目切换时调用
    loadScoreData() {
      // console.log('尝试加载分数数据');
      // 检查是否有answers[6]数据
      if (Array.isArray(this.answers[6]) && this.answers[6].length > 0) {
        // console.log('发现已有分数数据，开始加载:', JSON.stringify(this.answers[6]));

        // 初始化科目标识符映射
        const subjectMapping = {
          '语文': 'chinese',
          '数学': 'math',
          '英语': 'english',
          '外语': 'english',
          '物理': 'physics',
          '化学': 'chemistry',
          '生物': 'biology',
          '政治': 'politics',
          '历史': 'history',
          '地理': 'geography',
          '技术': 'tech'
        };

        // 从answers[6]将分数同步到subjectScores
        this.answers[6].forEach(subject => {
          if (subject && subject.name) {
            const subjectId = subjectMapping[subject.name] || subject.name.toLowerCase().replace(/\s+/g, '_');
            if (subjectId) {
              // 如果分数不是空值或undefined或null，则同步到subjectScores
              if (subject.score !== undefined && subject.score !== null && subject.score !== '') {
                // console.log(`加载科目分数: ${subject.name} (${subjectId}) = ${subject.score}`);
                this.$set(this.subjectScores, subjectId, String(subject.score));
              }
            }
          }
        });

        // 更新fsList数组
        this.updateFsList();
        // console.log('完成分数加载，当前subjectScores:', this.subjectScores);
        this.$forceUpdate(); // 强制更新视图
      }
    },

    // 根据ID获取问题对象
    getQuestionById(id) {
      return this.questions.find(q => q.id === id) || { content: '加载中...', questionChoiceDoS: [] };
    },

    getDisplayQuestionNumber() {
      if (!this.questions || this.questions.length === 0) {
        return this.currentQuestion + 1; // 默认返回
      }

      // ID为12的题目已经被过滤掉，直接返回正常序号
      return this.currentQuestion + 1;
    },

    // 根据专业ID获取专业名称
    getMajorNameById(majorId) {
      // 如果已经有缓存的名称，直接返回
      if (this.majorNameCache && this.majorNameCache[majorId]) {
        return this.majorNameCache[majorId];
      }

      // 遍历所有专业类别
      for (const category of this.items) {
        // 检查当前类别的子项
        if (category.children && category.children.length > 0) {
          for (const subCategory of category.children) {
            // 检查子类别的专业
            if (subCategory.children && subCategory.children.length > 0) {
              // 查找匹配ID的专业
              const major = subCategory.children.find(major => major.id === majorId);
              if (major) {
                const name = major.text || major.name;
                // 缓存专业名称
                if (!this.majorNameCache) this.majorNameCache = {};
                this.majorNameCache[majorId] = name;
                return name;
              }
            }
          }
        }
      }
      // 如果找不到，返回简化的ID
      return `专业${String(majorId).slice(-4)}`;
    },

    // 通过ID直接选择/取消选择专业
    directSelectMajorById(majorId) {
      // 查找专业对象
      let majorItem = null;

      // 遍历所有专业类别查找匹配的专业
      for (const category of this.items) {
        if (category.children && category.children.length > 0) {
          for (const subCategory of category.children) {
            if (subCategory.children && subCategory.children.length > 0) {
              const major = subCategory.children.find(major => major.id === majorId);
              if (major) {
                majorItem = major;
                break;
              }
            }
          }
          if (majorItem) break;
        }
      }

      // 如果找到了专业，调用directSelectMajor方法
      if (majorItem) {
        this.directSelectMajor(majorItem);
      } else {
        // 如果在当前标签页中找不到专业，直接从已选专业中移除
        const index = this.yxzy_ids.indexOf(majorId);
        if (index !== -1) {
          // 从已选专业中移除
          this.yxzy_ids.splice(index, 1);

          // 获取专业名称（从缓存中获取）
          const majorName = this.majorNameCache && this.majorNameCache[majorId] ?
              this.majorNameCache[majorId] : `专业${String(majorId).slice(-4)}`;

          // 显示提示
          uni.showToast({
            title: '已取消选择: ' + majorName,
            icon: 'none',
            duration: 1000
          });

          // 更新答案并检查状态
          this.$set(this.answers, 7, this.yxzy_ids);
          this.checkAnswerStatus();

          // 同步更新组件的activeIds
          if (this.$refs.treeSelect) {
            this.$refs.treeSelect.active_ids = this.yxzy_ids;
            this.$refs.treeSelect.initBadge();
          }

          // 强制刷新组件
          this.$forceUpdate();
        }
      }
    },

    // 格式化树形数据，将name属性转换为text属性
    formatTreeData(data) {
      if (!data || !Array.isArray(data)) return [];

      // 先过滤掉空的类别和子类别
      const filteredData = data.filter(item => {
        // 如果没有子项，则返回true（保留该项）
        if (!item.children || !Array.isArray(item.children)) return true;

        // 如果有子项，则检查子项是否为空
        if (item.children.length === 0) return false;

        // 如果是类别，检查是否有非空的子类别
        if (item.children[0].children) {
          // 检查是否有非空的子类别
          const hasNonEmptySubCategory = item.children.some(subCategory => {
            return subCategory.children && subCategory.children.length > 0;
          });
          return hasNonEmptySubCategory;
        }

        return true;
      });

      return filteredData.map(item => {
        const newItem = {
          ...item,
          text: item.name, // 将name复制到text属性
        };

        // 如果有children，递归处理
        if (item.children && Array.isArray(item.children)) {
          // 过滤掉空的子类别
          const filteredChildren = item.children.filter(child => {
            if (!child.children || !Array.isArray(child.children)) return true;
            return child.children.length > 0;
          });

          newItem.children = this.formatTreeData(filteredChildren);
        }

        return newItem;
      });
    },
    transformObject(obj) {
      // console.log('444')
      // console.log(obj)
      const result = {};
      Object.entries(obj).forEach(([key, value]) => {
        // 判断是否为带有冒号的字符串数组
        if (Array.isArray(value) && value.length > 0 &&
            typeof value[0] === 'string' && value[0].includes(':')) {

          // 将带有冒号的字符串数组转换为带score的对象数组
          result[key] = value.map(item => {
            const parts = item.split(':');
            return {
              name: parts[0],
              score: parseInt(parts[1])
            };
          });
        }
        // 判断数组只有一个元素的情况
        else if (Array.isArray(value) && value.length === 1) {
          result[key] = value[0];
        }
        // 其他情况保持不变
        else {
          if (value.includes(':')) {
            let arr = value.split(',')
            result[key] = arr.map(item => {
              const parts = item.split(':');
              return {
                name: parts[0],
                score: parseInt(parts[1])
              };
            });
          } else {
            result[key] = value;
          }

        }
      });

      return result;
    },

    getAnser() {
      let obj = {
        1: [15],
        2: [33],
        3: [37, 38],
        6: ["语文:100", "数学:150", "英语:150", "物理:100", "政治:90", "历史:80"],
        7: ['01102', '0345', '01121'],
        8: [66],
        9: [68],
        10: [70],
        11: [72],
        12: [78],
        13: '税务局',
        14: [80],
        15: [84]
      }

      let myobjs = Object.keys(obj).reduce((acc, key) => {
        acc[key] = obj[key].length === 1 ? obj[key][0] : obj[key];
        return acc;
      }, {});
      // console.log(myobjs)
      let result = this.transformObject(obj)
      // console.log(result)
      let myarr = Object.values(myobjs)


      // this.questions.map((myitems, myindexs) => {
      // 	if (myitems.type == 3 && myitems.id == 6) {
      // 		// console.log(myobjs[myitems.id])
      // 		myitems.subjects = []
      // 		let arr3 = []
      // 		myobjs[myitems.id].map((newitems2) => {
      // 			let arr = newitems2.split(':')
      // 			// console.log(arr)
      // 			myitems.subjects.push({
      // 				name: arr[0],
      // 				score: arr[1]
      // 			})
      // 			this.fsList.push(arr[0] + ':' + arr[1])
      // 			arr3.push({
      // 				score: arr[1]
      // 			})
      // 			newitems2 = arr3
      // 			return newitems2
      // 		})
      // 		// console.log(myitems)
      // 	}


      // 	myitems.questionChoiceDoS.map((newitems, newindexs) => {
      // 		newitems.checked = false
      // 		if (myitems.type == 1 && myobjs[myitems.id] == newitems.id) {
      // 			newitems.checked = true
      // 		}
      // 		if (myitems.id == 3 && myitems.type == 2 && (myobjs[myitems.id]).includes(newitems
      // 				.id)) {
      // 			newitems.checked = true
      // 			if (myitems.id == 3) {
      // 				this.fkList = myobjs[myitems.id]
      // 			}
      // 		}
      // 		if (myitems.type == 2 && myitems.id == 7) {
      // 			this.yxzy_ids = myobjs[myitems.id]
      // 		}
      // 		return newitems
      // 	})

      // })
      // // console.log(this.questions)


      this.questions.forEach(question => {
        const objValue = result[question.id];
        if (question.type === 1) {
          question.questionChoiceDoS.forEach(choice => {
            if (choice.id === objValue) {
              choice.checked = true;
            }
          });
        } else if (question.type === 2) {
          if (Array.isArray(objValue)) {
            question.questionChoiceDoS.forEach(choice => {
              if (objValue.includes(choice.id)) {
                choice.checked = true;
              }
            });
          }
        } else if (question.type === 3) {

          if (question.questionChoiceDoS.length > 0) {
            question.questionChoiceDoS[0].value = objValue;
          }
        }
        if (question.id === 6 && obj[6] && Array.isArray(obj[6])) {
          question.subjects = obj[6].map(item => {
            const [name, score] = item.split(':');
            return {
              name: name.trim(),
              score: score.trim()
            };
          });
        }
      });

      if (obj[6] && Array.isArray(obj[6])) {
        this.fkList = obj[6].map(item => {
          const [name, score] = item.split(':');
          return {
            name: name.trim(),
            score: score.trim()
          };
        });
        this.fsList = this.fkList
      }


      if (obj[7] && Array.isArray(obj[7])) {
        this.yxzy_ids = obj[7];
      }
      // console.log(this.questions)
      this.currentQuestion = myarr.length
      this.answers = result
      this.calculateTotalScore()
      return this.questions;
      // console.log(this.answers)
    },
    findNamesById(arr, items) {
      items.forEach(item => {
        if (arr.includes(item.id)) {
          this.correspondingNames.push(item.name);
          return this.correspondingNames
        }
        if (item.children) {
          this.findNamesById(arr, item.children);
        }
      }).finally(() => {
        // 隐藏加载提示
        uni.hideLoading();
      });
    },
    // 切换教育层次
    changeEducationLevel(level) {
      // 防止重复点击
      if (this.isProcessingSelection) {
        return;
      }

      // 如果处于搜索模式，先清除搜索
      if (this.isSearchMode) {
        this.clearSearch();
      }

      // 标准化传入的教育层次名称
      level = this.normalizeEducationLevel(level);

      // 如果点击的是当前层次，不做任何操作
      if (this.currentEducationLevel === level) {
        return;
      }

      // 检查目标教育层次是否有专业
      if (this.educationLevelCounts[level] === 0) {
        uni.showToast({
          title: `${level} 暂无专业数据`,
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 设置处理标志
      this.isProcessingSelection = true;

      try {
        // 设置当前是否要按教育层次过滤
        this.filterByEducationLevel = true;

        // 更新当前教育层次
        this.currentEducationLevel = level;

        // 如果有推荐专业数据，直接重新处理
        if (this.recommendedMajorsData) {
          // 重新处理推荐专业数据，根据当前教育层次筛选
          this.processRecommendedMajors(this.recommendedMajorsData);

          // 检查当前选中的专业是否在新的教育层次中存在
          if (this.yxzy_ids && this.yxzy_ids.length > 0) {
            const majorId = this.yxzy_ids[0];
            let majorExists = false;

            // 检查专业是否存在于当前教育层次
            outerLoop: for (const category of this.items) {
              if (category.children) {
                for (const subCategory of category.children) {
                  if (subCategory.children) {
                    for (const major of subCategory.children) {
                      if (major.id === majorId) {
                        majorExists = true;
                        break outerLoop;
                      }
                    }
                  }
                }
              }
            }

            // 如果专业不存在于当前教育层次，清空选择
            if (!majorExists) {
              this.yxzy_ids = [];
              this.$set(this.answers, 7, this.yxzy_ids);
              uni.showToast({
                title: '已切换教育层次，原选择的专业不可用',
                icon: 'none',
                duration: 2000
              });
            }
          }
        } else {
          // 如果没有推荐专业数据，则重新获取
          this.getSubject();
        }

      } finally {
        // 延时解除锁定
        setTimeout(() => {
          this.isProcessingSelection = false;
        }, 300);
      }
    },

    // 搜索专业 - 使用API接口查询
    async searchMajors() {
      if (!this.searchKeyword.trim()) {
        // 如果搜索关键字为空，恢复原始数据
        if (this.originalItems) {
          this.items = JSON.parse(JSON.stringify(this.originalItems));
          this.isSearchMode = false; // 退出搜索模式
        } else if (this.recommendedMajorsData) {
          // 如果有推荐专业数据，重新处理
          this.processRecommendedMajors(this.recommendedMajorsData);
          this.isSearchMode = false; // 退出搜索模式
        } else {
          // 如果没有原始数据，重新获取所有专业
          await this.getSubject();
          this.isSearchMode = false; // 退出搜索模式
        }
        return;
      }

      // 显示加载提示
      uni.showLoading({
        title: '搜索中...'
      });

      try {
        // 如果还没有保存原始数据，先保存当前数据
        if (!this.originalItems) {
          this.originalItems = JSON.parse(JSON.stringify(this.items));
        }

        // 进入搜索模式
        this.isSearchMode = true;

        // 在本地推荐专业数据中搜索，只搜索当前教育层次的专业
        if (this.recommendedMajorsData && this.recommendedMajorsData.categories) {
          const keyword = this.searchKeyword.trim().toLowerCase();
          const currentLevel = this.currentEducationLevel;
          console.log('搜索关键词:', keyword, '当前教育层次:', currentLevel);

          // 创建一个深拷贝以进行过滤
          const filteredCategories = JSON.parse(JSON.stringify(this.recommendedMajorsData.categories));

          // 过滤专业
          const searchResults = filteredCategories
            .map(category => {
              // 过滤子类别
              if (category.children && category.children.length > 0) {
                category.children = category.children
                  .map(subCategory => {
                    // 过滤专业
                    if (subCategory.children && subCategory.children.length > 0) {
                      subCategory.children = subCategory.children.filter(major => {
                        // 首先检查是否匹配当前教育层次
                        const matchesEducationLevel = !major.educationLevel ||
                          this.normalizeEducationLevel(major.educationLevel) === this.normalizeEducationLevel(currentLevel);
                        if (!matchesEducationLevel) return false;

                        // 然后检查是否匹配搜索关键词
                        return major.name.toLowerCase().includes(keyword) ||
                               (major.code && major.code.toLowerCase().includes(keyword));
                      });
                    }
                    return subCategory;
                  })
                  .filter(subCategory => subCategory.children && subCategory.children.length > 0);
              }
              return category;
            })
            .filter(category => category.children && category.children.length > 0);

          // 转换为组件需要的格式
          const formattedResults = searchResults.map((item) => {
            let categoryItem = {
              id: item.id,
              name: item.name,
              text: item.name.substring(0, item.name.length - 2),
              badge: 'number',
              children: [],
              loaded: true
            };

            if (item.children && item.children.length > 0) {
              categoryItem.children = item.children.map((newitem) => {
                let subCategoryItem = {
                  id: newitem.id,
                  name: newitem.name,
                  text: newitem.name,
                  isshow: true,
                  children: []
                };

                if (newitem.children && newitem.children.length > 0) {
                  subCategoryItem.children = newitem.children.map((mynewitem) => {
                    return {
                      id: mynewitem.id,
                      name: mynewitem.name,
                      text: mynewitem.name,
                      code: mynewitem.code,
                      educationLevel: mynewitem.educationLevel,
                      isRecommended: mynewitem.isRecommended || false,
                      careerDirection: mynewitem.careerDirection || null,
                      duration: mynewitem.duration || '四年',
                      degree: mynewitem.degree || '学士',
                      salary: mynewitem.salary || '￥10.3万',
                      graduateScale: mynewitem.graduateScale || null,
                      maleFemaleRatio: mynewitem.maleFemaleRatio || null
                    };
                  });
                }

                return subCategoryItem;
              });
            }

            return categoryItem;
          });

          // 更新显示的数据
          this.items = formattedResults;

          // 重置左侧导航选中状态为第一项
          if (this.$refs.treeSelect) {
            this.$refs.treeSelect.active_index = 0;
          }

          // 显示搜索结果提示
          if (formattedResults.length === 0 || formattedResults.every(cat => !cat.children || cat.children.length === 0)) {
            uni.showToast({
              title: '未找到相关专业',
              icon: 'none'
            });
          }
        } else {
          // 如果没有推荐专业数据，显示提示
          uni.showToast({
            title: '无法搜索专业，请重试',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('专业搜索失败:', error);
        uni.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    // 清除搜索
    clearSearch() {
      // 防止重复点击
      if (this.isProcessingSelection) {
        return;
      }

      this.searchKeyword = '';
      this.isSearchMode = false; // 退出搜索模式

      if (this.originalItems) {
        this.items = JSON.parse(JSON.stringify(this.originalItems));
      }
    },

    async getSubject(categoryId = '') {
      // 如果已经有推荐专业数据，直接使用
      if (this.recommendedMajorsData) {
        // console.log('使用已加载的推荐专业数据');

        // 如果传入了categoryId，则只更新该类别的数据
        if (categoryId && this.items.length > 0) {
          // 在现有数据中找到对应的类别索引
          const categoryIndex = this.items.findIndex(item => item.id === categoryId);

          if (categoryIndex !== -1) {
            // 从推荐专业数据中找到对应的类别
            const currentCategory = this.recommendedMajorsData.categories.find(item => item.id === categoryId);

            if (currentCategory) {
              // 处理二级分类数据
              let children = [];
              if (currentCategory.children && currentCategory.children.length > 0) {
                children = currentCategory.children.map((newitem) => {
                  let subCategoryItem = {
                    id: newitem.id,
                    name: newitem.name,
                    text: newitem.name,
                    isshow: true,
                    children: []
                  };

                  if (newitem.children && newitem.children.length > 0) {
                    subCategoryItem.children = newitem.children.map((mynewitem) => {
                      return {
                        id: mynewitem.id,
                        name: mynewitem.name,
                        text: mynewitem.name,
                        code: mynewitem.code,
                        educationLevel: mynewitem.educationLevel
                      };
                    });
                  }

                  return subCategoryItem;
                });
              }

              // 更新类别数据
              this.items[categoryIndex].children = children;
              this.items[categoryIndex].loaded = true;
              // console.log('类别数据已更新:', categoryId);
            }
          }
        } else {
          // 如果没有传入categoryId或者items为空，则重新处理所有数据
          this.processRecommendedMajors(this.recommendedMajorsData);
        }
        return;
      }

      // 如果没有推荐专业数据，则尝试获取用户信息
      if (!this.userInfo) {
        // console.log('没有用户信息，尝试获取用户信息');
        await this.getUserInfo();
        return;
      }

      // 如果仍然没有推荐专业数据，则显示提示
      console.error('无法获取专业数据');
      uni.showToast({
        title: '无法获取专业数据，请重试',
        icon: 'none'
      });
    },
    changemyItem(item) {
      item.checked = !item.checked
    },
    changeLeftNav(item) {
      this.leftNavList.map((items) => {
        items.checked = false
        if (items.id == item.id) {
          items.checked = true
        }
      })
      // item.checked = !item.checked
    },
    getRecordAnswer() {
      // console.log('开始获取已填写的答案，answerNo:', this.answerNo);
      this.$apis.getRecordAnswer({
        answerNo: this.answerNo
      }).then((res) => {
        if (res.code == 0) {
          // console.log('获取到的答案数据:', res.data);
          let obj = res.data
          let myobjs = Object.keys(obj).reduce((acc, key) => {
            acc[key] = obj[key].length === 1 ? obj[key][0] : obj[key];
            return acc;
          }, {});
          // console.log('处理后的答案数据:', myobjs)
          let result = this.transformObject(obj)
          // console.log('转换后的答案数据:', result)
          let myarr = Object.values(myobjs)
          // this.questions.map((myitems, myindexs) => {
          // 	if (myitems.type == 3 && myitems.id == 6) {
          // 		// console.log(myobjs[myitems.id])
          // 		myitems.subjects = []
          // 		let arr3 = []
          // 		myobjs[myitems.id].map((newitems2) => {
          // 			let arr = newitems2.split(':')
          // 			// console.log(arr)
          // 			myitems.subjects.push({
          // 				name: arr[0],
          // 				score: arr[1]
          // 			})
          // 			this.fsList.push(arr[0] + ':' + arr[1])
          // 			arr3.push({
          // 				score: arr[1]
          // 			})
          // 			newitems2 = arr3
          // 			return newitems2
          // 		})
          // 		// console.log(myitems)
          // 	}


          // 	myitems.questionChoiceDoS.map((newitems, newindexs) => {
          // 		newitems.checked = false
          // 		if (myitems.type == 1 && myobjs[myitems.id] == newitems.id) {
          // 			newitems.checked = true
          // 		}
          // 		if (myitems.id == 3 && myitems.type == 2 && (myobjs[myitems.id]).includes(newitems
          // 				.id)) {
          // 			newitems.checked = true
          // 			if (myitems.id == 3) {
          // 				this.fkList = myobjs[myitems.id]
          // 			}
          // 		}
          // 		if (myitems.type == 2 && myitems.id == 7) {
          // 			this.yxzy_ids = myobjs[myitems.id]
          // 		}
          // 		return newitems
          // 	})

          // })
          // // console.log(this.questions)

          // console.log('8889999')
          this.questions.forEach(question => {
            const objValue = result[question.id];
            if (question.type === 1) {
              question.questionChoiceDoS.forEach(choice => {
                if (choice.id === objValue) {
                  choice.checked = true;
                }
              });
            } else if (question.type === 2) {
              if (Array.isArray(objValue)) {
                question.questionChoiceDoS.forEach(choice => {
                  if (objValue.includes(choice.id)) {
                    choice.checked = true;
                  }
                });
              }
            } else if (question.type === 3) {

              if (question.questionChoiceDoS.length > 0) {
                question.questionChoiceDoS[0].value = objValue;
              }
            }
            if (question.id === 6 && obj[6]) {
              // console.log('处理问题6（科目分数）的数据:', obj[6]);

              // 初始化 subjectScores 对象（如果不存在）
              if (!this.subjectScores) {
                this.subjectScores = {};
              }

              // 将字符串分割为科目分数对
              let arr = obj[6].split(',')
              const subjectItems = arr.map(item => {
                const parts = item.split(':');
                const name = parts[0].trim();
                const score = parts.length > 1 ? parts[1].trim() : '';

                // 打印每个科目的解析结果
                // console.log('解析科目:', name, '分数:', score);

                // 根据科目名称确定ID
                let subjectId = '';
                switch(name) {
                  case '语文': subjectId = 'chinese'; break;
                  case '数学': subjectId = 'math'; break;
                  case '英语': subjectId = 'english'; break;
                  case '外语': subjectId = 'english'; break;
                  case '物理': subjectId = 'physics'; break;
                  case '化学': subjectId = 'chemistry'; break;
                  case '生物': subjectId = 'biology'; break;
                  case '政治': subjectId = 'politics'; break;
                  case '历史': subjectId = 'history'; break;
                  case '地理': subjectId = 'geography'; break;
                  case '技术': subjectId = 'tech'; break;
                  default: subjectId = name.toLowerCase().replace(/\s+/g, '_');
                }

                // console.log('科目 ID 映射:', name, '->', subjectId);

                // 直接更新 subjectScores
                if (score) {
                  this.$set(this.subjectScores, subjectId, score);
                  // console.log('设置分数:', subjectId, '=', score);
                }

                return {
                  name: name,
                  score: score
                };
              });

              // console.log('解析后的科目分数数据:', JSON.stringify(subjectItems));
              // console.log('已更新 subjectScores:', JSON.stringify(this.subjectScores));

              // 更新question.subjects
              question.subjects = subjectItems;

              // 强制更新视图
              this.$forceUpdate();
            }
          });

          if (obj[3] && this.ver == 2 && !obj[6]) {
            // console.log('44444444555')
            // console.log(obj[3])
            const subjects = obj[3].map(subject => ({
              name: this.LabelFn(subject, this.questions[2].questionChoiceDoS),
              score: ''
            }));
            // console.log(subjects)
            let arr = [{
              name: '语文',
              score: ''
            },
              {
                name: '数学',
                score: ''
              },
              {
                name: '英语',
                score: ''
              }
            ];
            this.fkList = arr.concat(subjects)
            // console.log(this.fkList)
            this.questions[3].subjects = this.fkList
            this.fsList = this.fkList
          }

          if (obj[6]) {
            // console.log('处理问题6的数据（在外层）:', obj[6]);

            // 确保初始化 subjectScores
            if (!this.subjectScores) {
              this.subjectScores = {};
            }

            // 将字符串分割为科目分数对
            let arr = obj[6].split(',')
            this.fkList = arr.map(item => {
              const parts = item.split(':');
              const name = parts[0].trim();
              const score = parts.length > 1 ? parts[1].trim() : '';

              // console.log('外层解析科目:', name, '分数:', score);

              // 根据科目名称确定ID
              let subjectId = '';
              switch(name) {
                case '语文': subjectId = 'chinese'; break;
                case '数学': subjectId = 'math'; break;
                case '英语': subjectId = 'english'; break;
                case '外语': subjectId = 'english'; break;
                case '物理': subjectId = 'physics'; break;
                case '化学': subjectId = 'chemistry'; break;
                case '生物': subjectId = 'biology'; break;
                case '政治': subjectId = 'politics'; break;
                case '历史': subjectId = 'history'; break;
                case '地理': subjectId = 'geography'; break;
                case '技术': subjectId = 'tech'; break;
                default: subjectId = name.toLowerCase().replace(/\s+/g, '_');
              }

              // console.log('外层科目 ID 映射:', name, '->', subjectId);

              // 直接更新 subjectScores
              if (score) {
                this.$set(this.subjectScores, subjectId, score);
                // console.log('外层设置分数:', subjectId, '=', score);
              }

              return {
                name: name,
                score: score
              };
            });

            this.fsList = this.fkList;
            // console.log('已更新 fkList:', JSON.stringify(this.fkList));
            // console.log('已更新 subjectScores:', JSON.stringify(this.subjectScores));

            // 初始化科目列表
            if (this.questions && this.questions.length > 0) {
              const question6 = this.questions.find(q => q.id === 6);
              if (question6) {
                // 设置科目列表
                question6.subjects = this.fkList;
                // console.log('已设置问题6的subjects:', JSON.stringify(question6.subjects));
              }
            }

            // 如果当前题目是科目分数题，则强制更新界面
            if (this.questions && this.questions.length > 0 && this.currentQuestion >= 0 &&
                this.currentQuestion < this.questions.length &&
                this.questions[this.currentQuestion].id === 6) {
              // console.log('当前正在科目分数题，强制更新界面');

              // 将分数数据直接设置到当前题目的subjects中
              this.subjects = [...this.fkList];
              // console.log('已设置当前 subjects:', JSON.stringify(this.subjects));

              // 强制更新界面
              this.$forceUpdate();

              // 更新当前题目的答题状态
              this.updateCurrentQuestionAnsweredStatus();
            }
          }


          if (obj[7] && Array.isArray(obj[7])) {
            this.yxzy_ids = obj[7];
          }
          // console.log('处理后的问题数组:', this.questions);

          // 如果有分数数据，打印出来查看
          if (this.subjectScores) {
            // console.log('当前的 subjectScores:', this.subjectScores);
          }

          // 将答案数据设置到 answers 对象中
          this.answers = result;
          // console.log('设置后的 answers:', this.answers);

          // 计算总分
          this.calculateTotalScore();

          // 如果当前题目是第一题，则保持在第一题，否则设置为答案数量
          if (this.currentQuestion === 0) {
            // console.log('当前是第一题，保持当前题目不变');
          } else {
            // 设置当前题目为答案数量
            this.currentQuestion = myarr.length;
            // console.log('设置当前题目为:', this.currentQuestion);
          }

          // 更新当前题目的答题状态
          this.$nextTick(() => {
            this.updateCurrentQuestionAnsweredStatus();

            // 强制刷新视图
            this.$forceUpdate();
          });

          return this.questions;

          // let myobjs = Object.keys(obj).reduce((acc, key) => {
          // 	acc[key] = obj[key].length === 1 ? obj[key][0] : obj[key];
          // 	return acc;
          // }, {});
          // // console.log(myobjs)
          // let result = this.transformObject(obj)
          // // console.log(result)
          // let myarr = Object.values(myobjs)


          // this.questions.map((myitems, myindexs) => {
          // 	if (myitems.type == 3 && myitems.id == 6) {
          // 		// console.log(myobjs[myitems.id])
          // 		myitems.subjects = []
          // 		let arr3 = []
          // 		if (myobjs[myitems.id].length > 0) {
          // 			myobjs[myitems.id].map((newitems2) => {

          // 				let arr = newitems2.split(':')
          // 				// console.log(arr)
          // 				myitems.subjects.push({
          // 					name: arr[0],
          // 					score: arr[1]
          // 				})

          // 				this.fsList.push(arr[0] + ':' + arr[1])
          // 				arr3.push({
          // 					name: arr[0],
          // 					score: arr[1]
          // 				})
          // 				newitems2 = arr3
          // 				return newitems2
          // 			})
          // 		}

          // 		// console.log(myitems)
          // 	}

          // 	if (myitems.questionChoiceDoS.length > 0) {
          // 		myitems.questionChoiceDoS.map((newitems, newindexs) => {
          // 			newitems.checked = false
          // 			if (myitems.type == 1 && myobjs[myitems.id] == newitems.id) {
          // 				newitems.checked = true
          // 			}
          // 			// console.log(myobjs[myitems.id])
          // 			// console.log(newitems.id)
          // 			if (myitems.type == 2 && (myobjs[myitems.id]).includes(newitems
          // 					.id)) {
          // 				newitems.checked = true
          // 				if (myitems.id == 3) {
          // 					this.fkList = myobjs[myitems.id]
          // 				}
          // 			}
          // 			if (myitems.type == 2 && myitems.id == 7) {
          // 				this.yxzy_ids = myobjs[myitems.id]
          // 			}
          // 			return newitems
          // 		})
          // 	}
          // })
          // // console.log(this.questions)
          // this.currentQuestion = myarr.length
          // this.answers = result
        }
      })
    },
    getquestionList() {
      // this.questions = [{
      // 	"id": 1,
      // 	"content": "学生所处的高考省份",
      // 	"type": 1,
      // 	"isNecessary": 1,
      // 	"status": 1,
      // 	"sort": 1,
      // 	"questionChoiceDoS": [{
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 1,
      // 		"questionId": 1,
      // 		"choiceContent": "北京",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 2,
      // 		"questionId": 1,
      // 		"choiceContent": "天津",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 3,
      // 		"questionId": 1,
      // 		"choiceContent": "河北",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 4,
      // 		"questionId": 1,
      // 		"choiceContent": "山西",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 5,
      // 		"questionId": 1,
      // 		"choiceContent": "内蒙古",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 6,
      // 		"questionId": 1,
      // 		"choiceContent": "辽宁",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 7,
      // 		"questionId": 1,
      // 		"choiceContent": "吉林",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 8,
      // 		"questionId": 1,
      // 		"choiceContent": "黑龙江",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 9,
      // 		"questionId": 1,
      // 		"choiceContent": "上海",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 10,
      // 		"questionId": 1,
      // 		"choiceContent": "江苏",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 11,
      // 		"questionId": 1,
      // 		"choiceContent": "浙江",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 12,
      // 		"questionId": 1,
      // 		"choiceContent": "安徽",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 13,
      // 		"questionId": 1,
      // 		"choiceContent": "福建",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 14,
      // 		"questionId": 1,
      // 		"choiceContent": "江西",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 15,
      // 		"questionId": 1,
      // 		"choiceContent": "山东",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 16,
      // 		"questionId": 1,
      // 		"choiceContent": "河南",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 17,
      // 		"questionId": 1,
      // 		"choiceContent": "湖北",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 18,
      // 		"questionId": 1,
      // 		"choiceContent": "湖南",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 19,
      // 		"questionId": 1,
      // 		"choiceContent": "广东",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 20,
      // 		"questionId": 1,
      // 		"choiceContent": "广西",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 21,
      // 		"questionId": 1,
      // 		"choiceContent": "海南",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 22,
      // 		"questionId": 1,
      // 		"choiceContent": "重庆",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 23,
      // 		"questionId": 1,
      // 		"choiceContent": "四川",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 24,
      // 		"questionId": 1,
      // 		"choiceContent": "贵州",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 25,
      // 		"questionId": 1,
      // 		"choiceContent": "云南",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 26,
      // 		"questionId": 1,
      // 		"choiceContent": "西藏",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 27,
      // 		"questionId": 1,
      // 		"choiceContent": "陕西",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 28,
      // 		"questionId": 1,
      // 		"choiceContent": "甘肃",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 29,
      // 		"questionId": 1,
      // 		"choiceContent": "青海",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 30,
      // 		"questionId": 1,
      // 		"choiceContent": "宁夏",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 31,
      // 		"questionId": 1,
      // 		"choiceContent": "新疆",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}]
      // }, {
      // 	"id": 2,
      // 	"content": "学生性别",
      // 	"type": 1,
      // 	"isNecessary": 1,
      // 	"status": 1,
      // 	"sort": 2,
      // 	"questionChoiceDoS": [{
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 32,
      // 		"questionId": 2,
      // 		"choiceContent": "我是男生",
      // 		"sort": 1,
      // 		"type": 2,
      // 		"imageUrl": "xxxxxxxxxxx"
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 33,
      // 		"questionId": 2,
      // 		"choiceContent": "我是女生",
      // 		"sort": 1,
      // 		"type": 2,
      // 		"imageUrl": "xxxxxxxxxxx"
      // 	}]
      // }, {
      // 	"id": 3,
      // 	"content": "学生选科",
      // 	"type": 2,
      // 	"isNecessary": 1,
      // 	"status": 1,
      // 	"sort": 3,
      // 	"questionChoiceDoS": [{
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 34,
      // 		"questionId": 3,
      // 		"choiceContent": "物理",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 35,
      // 		"questionId": 3,
      // 		"choiceContent": "化学",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 36,
      // 		"questionId": 3,
      // 		"choiceContent": "生物",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 37,
      // 		"questionId": 3,
      // 		"choiceContent": "政治",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 38,
      // 		"questionId": 3,
      // 		"choiceContent": "历史",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 39,
      // 		"questionId": 3,
      // 		"choiceContent": "地理",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 40,
      // 		"questionId": 3,
      // 		"choiceContent": "技术",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}]
      // }, {
      // 	"id": 6,
      // 	"content": "各科分数",
      // 	"type": 3,
      // 	"isNecessary": 1,
      // 	"status": 1,
      // 	"sort": 6,
      // 	"questionChoiceDoS": [{
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 41,
      // 		"questionId": 6,
      // 		"choiceContent": "语文分",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 42,
      // 		"questionId": 6,
      // 		"choiceContent": "数学分",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 43,
      // 		"questionId": 6,
      // 		"choiceContent": "外语分",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 44,
      // 		"questionId": 6,
      // 		"choiceContent": "物理分",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 45,
      // 		"questionId": 6,
      // 		"choiceContent": "化学分",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 46,
      // 		"questionId": 6,
      // 		"choiceContent": "政治分",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}]
      // }, {
      // 	"id": 7,
      // 	"content": "意向专业类别",
      // 	"type": 2,
      // 	"isNecessary": 1,
      // 	"status": 1,
      // 	"sort": 7,
      // 	"questionChoiceDoS": [{
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 47,
      // 		"questionId": 7,
      // 		"choiceContent": "工学",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 48,
      // 		"questionId": 7,
      // 		"choiceContent": "医学",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 49,
      // 		"questionId": 7,
      // 		"choiceContent": "文学",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 50,
      // 		"questionId": 7,
      // 		"choiceContent": "管理学",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 51,
      // 		"questionId": 7,
      // 		"choiceContent": "理学",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 52,
      // 		"questionId": 7,
      // 		"choiceContent": "经济学",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 53,
      // 		"questionId": 7,
      // 		"choiceContent": "法学",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 54,
      // 		"questionId": 7,
      // 		"choiceContent": "艺术学",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 55,
      // 		"questionId": 7,
      // 		"choiceContent": "教育学",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 56,
      // 		"questionId": 7,
      // 		"choiceContent": "农学",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 57,
      // 		"questionId": 7,
      // 		"choiceContent": "历史学",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 58,
      // 		"questionId": 7,
      // 		"choiceContent": "哲学",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 59,
      // 		"questionId": 7,
      // 		"choiceContent": "林学类",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 60,
      // 		"questionId": 7,
      // 		"choiceContent": "水产类",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 61,
      // 		"questionId": 7,
      // 		"choiceContent": "草学类",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 62,
      // 		"questionId": 7,
      // 		"choiceContent": "植物生产类",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 63,
      // 		"questionId": 7,
      // 		"choiceContent": "动物医学类",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 64,
      // 		"questionId": 7,
      // 		"choiceContent": "动物生产类",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 65,
      // 		"questionId": 7,
      // 		"choiceContent": "自然保护与环境生态类",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}]
      // }, {
      // 	"id": 8,
      // 	"content": "性格",
      // 	"type": 1,
      // 	"isNecessary": 1,
      // 	"status": 1,
      // 	"sort": 8,
      // 	"questionChoiceDoS": [{
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 66,
      // 		"questionId": 8,
      // 		"choiceContent": "内向",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 67,
      // 		"questionId": 8,
      // 		"choiceContent": "外向",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}]
      // }, {
      // 	"id": 9,
      // 	"content": "学习能力",
      // 	"type": 1,
      // 	"isNecessary": 1,
      // 	"status": 1,
      // 	"sort": 9,
      // 	"questionChoiceDoS": [{
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 68,
      // 		"questionId": 9,
      // 		"choiceContent": "强",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 69,
      // 		"questionId": 9,
      // 		"choiceContent": "弱",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}]
      // }, {
      // 	"id": 10,
      // 	"content": "社交能力",
      // 	"type": 1,
      // 	"isNecessary": 1,
      // 	"status": 1,
      // 	"sort": 10,
      // 	"questionChoiceDoS": [{
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 70,
      // 		"questionId": 10,
      // 		"choiceContent": "强",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 71,
      // 		"questionId": 10,
      // 		"choiceContent": "弱",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}]
      // }, {
      // 	"id": 11,
      // 	"content": "家庭年收入（单位元）",
      // 	"type": 1,
      // 	"isNecessary": 1,
      // 	"status": 1,
      // 	"sort": 11,
      // 	"questionChoiceDoS": [{
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 72,
      // 		"questionId": 11,
      // 		"choiceContent": "小于2万",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 73,
      // 		"questionId": 11,
      // 		"choiceContent": "2万到5万",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 74,
      // 		"questionId": 11,
      // 		"choiceContent": "5万到10万",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 75,
      // 		"questionId": 11,
      // 		"choiceContent": "10万到20万",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 76,
      // 		"questionId": 11,
      // 		"choiceContent": "20万到50万",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 77,
      // 		"questionId": 11,
      // 		"choiceContent": "50万以上",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}]
      // }, {
      // 	"id": 12,
      // 	"content": "就业方向",
      // 	"type": 1,
      // 	"isNecessary": 1,
      // 	"status": 1,
      // 	"sort": 12,
      // 	"questionChoiceDoS": [{
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 78,
      // 		"questionId": 12,
      // 		"choiceContent": "体制内",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 79,
      // 		"questionId": 12,
      // 		"choiceContent": "体制外",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}]
      // }, {
      // 	"id": 13,
      // 	"content": "人脉资源",
      // 	"type": 3,
      // 	"isNecessary": 1,
      // 	"status": 1,
      // 	"sort": 13,
      // 	"questionChoiceDoS": []
      // }, {
      // 	"id": 14,
      // 	"content": "毕业去向",
      // 	"type": 1,
      // 	"isNecessary": 1,
      // 	"status": 1,
      // 	"sort": 14,
      // 	"questionChoiceDoS": [{
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 80,
      // 		"questionId": 14,
      // 		"choiceContent": "考公",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 81,
      // 		"questionId": 14,
      // 		"choiceContent": "专升本",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 82,
      // 		"questionId": 14,
      // 		"choiceContent": "考研",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 83,
      // 		"questionId": 14,
      // 		"choiceContent": "就业",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}]
      // }, {
      // 	"id": 15,
      // 	"content": "城市省份",
      // 	"type": 1,
      // 	"isNecessary": 1,
      // 	"status": 1,
      // 	"sort": 15,
      // 	"questionChoiceDoS": [{
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 84,
      // 		"questionId": 15,
      // 		"choiceContent": "省内",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}, {
      // 		"createTime": 1742905924000,
      // 		"updateTime": 1742905924000,
      // 		"creator": "admin",
      // 		"updater": "admin",
      // 		"deleted": false,
      // 		"id": 85,
      // 		"questionId": 15,
      // 		"choiceContent": "省外",
      // 		"sort": 1,
      // 		"type": 1,
      // 		"imageUrl": null
      // 	}]
      // }]

      // // console.log(this.questions)
      // this.questions = [{
      // 		"id": 1, //问题Id
      // 		"content": "学生所处的高考省份", //问题内容
      // 		"type": 1, //问题类型 1单选题 2多选题 3填空题
      // 		"isNecessary": 1, //是否必答题 0非必答 1必答
      // 		"status": 1, //问题状态 0无效 1有效 在这里都是有效
      // 		"sort": 1, //排序字段 1234 小在前
      // 		"questionChoiceDoS": [ //问题的选项
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 1, //选项的Id
      // 				"questionId": 1, //问题Id
      // 				"choiceContent": "北京", //选项的内容
      // 				"sort": 1, //排序字段 1234 从小到大排序
      // 				"type": 1, //选项类型 1文字选项 2图文选项
      // 				"imageUrl": null //如果是图文选项 这里是图片地址
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 2,
      // 				"questionId": 1,
      // 				"choiceContent": "天津",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 3,
      // 				"questionId": 1,
      // 				"choiceContent": "河北",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 4,
      // 				"questionId": 1,
      // 				"choiceContent": "山西",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 5,
      // 				"questionId": 1,
      // 				"choiceContent": "内蒙古",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 6,
      // 				"questionId": 1,
      // 				"choiceContent": "辽宁",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 7,
      // 				"questionId": 1,
      // 				"choiceContent": "吉林",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 8,
      // 				"questionId": 1,
      // 				"choiceContent": "黑龙江",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 9,
      // 				"questionId": 1,
      // 				"choiceContent": "上海",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 10,
      // 				"questionId": 1,
      // 				"choiceContent": "江苏",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 11,
      // 				"questionId": 1,
      // 				"choiceContent": "浙江",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 12,
      // 				"questionId": 1,
      // 				"choiceContent": "安徽",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 13,
      // 				"questionId": 1,
      // 				"choiceContent": "福建",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 14,
      // 				"questionId": 1,
      // 				"choiceContent": "江西",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 15,
      // 				"questionId": 1,
      // 				"choiceContent": "山东",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 16,
      // 				"questionId": 1,
      // 				"choiceContent": "河南",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 17,
      // 				"questionId": 1,
      // 				"choiceContent": "湖北",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 18,
      // 				"questionId": 1,
      // 				"choiceContent": "湖南",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 19,
      // 				"questionId": 1,
      // 				"choiceContent": "广东",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 20,
      // 				"questionId": 1,
      // 				"choiceContent": "广西",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 21,
      // 				"questionId": 1,
      // 				"choiceContent": "海南",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 22,
      // 				"questionId": 1,
      // 				"choiceContent": "重庆",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 23,
      // 				"questionId": 1,
      // 				"choiceContent": "四川",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 24,
      // 				"questionId": 1,
      // 				"choiceContent": "贵州",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 25,
      // 				"questionId": 1,
      // 				"choiceContent": "云南",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 26,
      // 				"questionId": 1,
      // 				"choiceContent": "西藏",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 27,
      // 				"questionId": 1,
      // 				"choiceContent": "陕西",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 28,
      // 				"questionId": 1,
      // 				"choiceContent": "甘肃",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 29,
      // 				"questionId": 1,
      // 				"choiceContent": "青海",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 30,
      // 				"questionId": 1,
      // 				"choiceContent": "宁夏",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 31,
      // 				"questionId": 1,
      // 				"choiceContent": "新疆",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			}
      // 		]
      // 	},
      // 	{
      // 		"id": 2,
      // 		"content": "学生性别",
      // 		"type": 1,
      // 		"isNecessary": 1,
      // 		"status": 1,
      // 		"sort": 2,
      // 		"questionChoiceDoS": [{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 32,
      // 				"questionId": 2,
      // 				"choiceContent": "我是男生",
      // 				"sort": 1,
      // 				"type": 2,
      // 				"imageUrl": "xxxxxxxxxxx"
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 33,
      // 				"questionId": 2,
      // 				"choiceContent": "我是女生",
      // 				"sort": 1,
      // 				"type": 2,
      // 				"imageUrl": "xxxxxxxxxxx"
      // 			}
      // 		]
      // 	},
      // 	{
      // 		"id": 3,
      // 		"content": "学生主科",
      // 		"type": 1,
      // 		"isNecessary": 1,
      // 		"status": 1,
      // 		"sort": 3,
      // 		"questionChoiceDoS": [{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 34,
      // 				"questionId": 3,
      // 				"choiceContent": "物理",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 35,
      // 				"questionId": 3,
      // 				"choiceContent": "历史",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			}
      // 		]
      // 	},
      // 	{
      // 		"id": 4,
      // 		"content": "学生副科（4选2）",
      // 		"type": 2,
      // 		"isNecessary": 1,
      // 		"status": 1,
      // 		"sort": 4,
      // 		"questionChoiceDoS": [{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 36,
      // 				"questionId": 4,
      // 				"choiceContent": "化学",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 37,
      // 				"questionId": 4,
      // 				"choiceContent": "生物",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 38,
      // 				"questionId": 4,
      // 				"choiceContent": "政治",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 39,
      // 				"questionId": 4,
      // 				"choiceContent": "地理",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			}
      // 		]
      // 	},
      // 	{
      // 		"id": 5,
      // 		"content": "高考总分",
      // 		"type": 3,
      // 		"isNecessary": 1,
      // 		"status": 1,
      // 		"sort": 5,
      // 		"questionChoiceDoS": [{
      // 			"createTime": 1742905924000,
      // 			"updateTime": 1742905924000,
      // 			"creator": "admin",
      // 			"updater": "admin",
      // 			"deleted": false,
      // 			"id": 40,
      // 			"questionId": 5,
      // 			"choiceContent": "分",
      // 			"sort": 1,
      // 			"type": 1,
      // 			"imageUrl": null
      // 		}]
      // 	},
      // 	{
      // 		"id": 6,
      // 		"content": "各科分数",
      // 		"type": 3,
      // 		"isNecessary": 1,
      // 		"status": 1,
      // 		"sort": 6,
      // 		"questionChoiceDoS": [{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 41,
      // 				"questionId": 6,
      // 				"choiceContent": "语文分",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 42,
      // 				"questionId": 6,
      // 				"choiceContent": "数学分",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 43,
      // 				"questionId": 6,
      // 				"choiceContent": "外语分",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 44,
      // 				"questionId": 6,
      // 				"choiceContent": "物理分",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 45,
      // 				"questionId": 6,
      // 				"choiceContent": "化学分",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 46,
      // 				"questionId": 6,
      // 				"choiceContent": "政治分",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			}
      // 		]
      // 	},
      // 	{
      // 		"id": 7,
      // 		"content": "意向专业类别",
      // 		"type": 2,
      // 		"isNecessary": 1,
      // 		"status": 1,
      // 		"sort": 7,
      // 		"questionChoiceDoS": [{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 47,
      // 				"questionId": 7,
      // 				"choiceContent": "工学",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 48,
      // 				"questionId": 7,
      // 				"choiceContent": "医学",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 49,
      // 				"questionId": 7,
      // 				"choiceContent": "文学",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 50,
      // 				"questionId": 7,
      // 				"choiceContent": "管理学",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 51,
      // 				"questionId": 7,
      // 				"choiceContent": "理学",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 52,
      // 				"questionId": 7,
      // 				"choiceContent": "经济学",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 53,
      // 				"questionId": 7,
      // 				"choiceContent": "法学",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 54,
      // 				"questionId": 7,
      // 				"choiceContent": "艺术学",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 55,
      // 				"questionId": 7,
      // 				"choiceContent": "教育学",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 56,
      // 				"questionId": 7,
      // 				"choiceContent": "农学",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 57,
      // 				"questionId": 7,
      // 				"choiceContent": "历史学",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 58,
      // 				"questionId": 7,
      // 				"choiceContent": "哲学",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 59,
      // 				"questionId": 7,
      // 				"choiceContent": "林学类",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 60,
      // 				"questionId": 7,
      // 				"choiceContent": "水产类",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 61,
      // 				"questionId": 7,
      // 				"choiceContent": "草学类",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 62,
      // 				"questionId": 7,
      // 				"choiceContent": "植物生产类",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 63,
      // 				"questionId": 7,
      // 				"choiceContent": "动物医学类",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 64,
      // 				"questionId": 7,
      // 				"choiceContent": "动物生产类",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 65,
      // 				"questionId": 7,
      // 				"choiceContent": "自然保护与环境生态类",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			}
      // 		]
      // 	},
      // 	{
      // 		"id": 8,
      // 		"content": "性格",
      // 		"type": 1,
      // 		"isNecessary": 1,
      // 		"status": 1,
      // 		"sort": 8,
      // 		"questionChoiceDoS": [{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 66,
      // 				"questionId": 8,
      // 				"choiceContent": "内向",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 67,
      // 				"questionId": 8,
      // 				"choiceContent": "外向",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			}
      // 		]
      // 	},
      // 	{
      // 		"id": 9,
      // 		"content": "学习能力",
      // 		"type": 1,
      // 		"isNecessary": 1,
      // 		"status": 1,
      // 		"sort": 9,
      // 		"questionChoiceDoS": [{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 68,
      // 				"questionId": 9,
      // 				"choiceContent": "强",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 69,
      // 				"questionId": 9,
      // 				"choiceContent": "弱",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			}
      // 		]
      // 	},
      // 	{
      // 		"id": 10,
      // 		"content": "社交能力",
      // 		"type": 1,
      // 		"isNecessary": 1,
      // 		"status": 1,
      // 		"sort": 10,
      // 		"questionChoiceDoS": [{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 70,
      // 				"questionId": 10,
      // 				"choiceContent": "强",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 71,
      // 				"questionId": 10,
      // 				"choiceContent": "弱",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			}
      // 		]
      // 	},
      // 	{
      // 		"id": 11,
      // 		"content": "家庭年收入（单位元）",
      // 		"type": 1,
      // 		"isNecessary": 1,
      // 		"status": 1,
      // 		"sort": 11,
      // 		"questionChoiceDoS": [{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 72,
      // 				"questionId": 11,
      // 				"choiceContent": "小于2万",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 73,
      // 				"questionId": 11,
      // 				"choiceContent": "2万到5万",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 74,
      // 				"questionId": 11,
      // 				"choiceContent": "5万到10万",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 75,
      // 				"questionId": 11,
      // 				"choiceContent": "10万到20万",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 76,
      // 				"questionId": 11,
      // 				"choiceContent": "20万到50万",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 77,
      // 				"questionId": 11,
      // 				"choiceContent": "50万以上",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			}
      // 		]
      // 	},
      // 	{
      // 		"id": 12,
      // 		"content": "就业方向",
      // 		"type": 1,
      // 		"isNecessary": 1,
      // 		"status": 1,
      // 		"sort": 12,
      // 		"questionChoiceDoS": [{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 78,
      // 				"questionId": 12,
      // 				"choiceContent": "体制内",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 79,
      // 				"questionId": 12,
      // 				"choiceContent": "体制外",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			}
      // 		]
      // 	},
      // 	{
      // 		"id": 13,
      // 		"content": "人脉资源",
      // 		"type": 3,
      // 		"isNecessary": 1,
      // 		"status": 1,
      // 		"sort": 13,
      // 		"questionChoiceDoS": []
      // 	},
      // 	{
      // 		"id": 14,
      // 		"content": "毕业去向",
      // 		"type": 1,
      // 		"isNecessary": 1,
      // 		"status": 1,
      // 		"sort": 14,
      // 		"questionChoiceDoS": [{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 80,
      // 				"questionId": 14,
      // 				"choiceContent": "考公",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 81,
      // 				"questionId": 14,
      // 				"choiceContent": "专升本",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 82,
      // 				"questionId": 14,
      // 				"choiceContent": "考研",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 83,
      // 				"questionId": 14,
      // 				"choiceContent": "就业",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			}
      // 		]
      // 	},
      // 	{
      // 		"id": 15,
      // 		"content": "城市省份",
      // 		"type": 1,
      // 		"isNecessary": 1,
      // 		"status": 1,
      // 		"sort": 15,
      // 		"questionChoiceDoS": [{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 84,
      // 				"questionId": 15,
      // 				"choiceContent": "省内",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			},
      // 			{
      // 				"createTime": 1742905924000,
      // 				"updateTime": 1742905924000,
      // 				"creator": "admin",
      // 				"updater": "admin",
      // 				"deleted": false,
      // 				"id": 85,
      // 				"questionId": 15,
      // 				"choiceContent": "省外",
      // 				"sort": 1,
      // 				"type": 1,
      // 				"imageUrl": null
      // 			}
      // 		]
      // 	}
      // ]
      // this.getAnser()
      this.$apis.getqList({
        type: this.ver
      }).then((res) => {
        if (res.code == 0) {
          res.data.map((item) => {
            if (item.id == 3) {
              item.type = 2
            }
            if (item.questionChoiceDoS.length > 0) {
              item.questionChoiceDoS.map((newitem) => {
                newitem.checked = false
              })
            }
          })
          // 过滤掉ID为12的题目（就业方向题）
          this.questions = res.data.filter(item => item.id !== 12)
          this.questions.map((item) => {
            if (item.questionChoiceDoS.length > 0) {
              item.questionChoiceDoS.map((newitem) => {
                newitem.hidden = false
                // 不再为技术选项设置特殊属性
              })
            }
          })

          // 设置ID为12题目的默认答案（虽然不显示，但需要在提交时包含）
          this.$set(this.answers, 12, 78); // 78是"体制内"选项的ID

          // 在问题列表加载完成后，获取用户信息
          this.getUserInfo()

          // 调试输出问题数组结构
          // console.log('问题数组结构:');
          this.questions.forEach((q, index) => {
            // console.log(`索引: ${index}, ID: ${q.id}, 内容: ${q.content}`);
          });

          // 打印当前题目索引和问题总数
          // console.log(`当前题目索引: ${this.currentQuestion}, 问题总数: ${this.questions.length}`);
          // 检查是否是最后一题
          if (this.currentQuestion === this.questions.length - 1) {
            // console.log('当前是最后一题');
            // console.log('最后一题内容:', this.questions[this.currentQuestion]);
          }

          // 问题列表加载完成后获取用户信息
          this.getUserInfo();

          if (this.answerNo && this.answerNo != '-1') {
            this.$nextTick(() => {
              this.getRecordAnswer()
            })
          }
        }
      })
    },
    goHome() {
      uni.switchTab({
        url: '/pages/index/index'
      })
    },
    close() {
      this.show = false
      // 关闭弹窗时返回上级页面
      uni.navigateBack({
        delta: 1
      })
    },
    open() {
      this.show = true
    },
    submitQuestionnaire() {
      // 使用节流函数包装提交逻辑，防止重复点击
      this.$common.throttle(this.submitReportToServer.bind(this), null, 2000);
    },

    // 将提交报告的逻辑抽离为单独的方法
    submitReportToServer() {
      // 防止重复提交
      if (this.isSubmitting) {
        uni.showToast({
          title: '数据正在处理，请勿重复提交',
          icon: 'none'
        });
        return;
      }

      // Assuming this.questions and this.answers are already defined
      this.answersInChinese = {};
      let myarr = []
      this.fsList.map((item) => {
        myarr.push(item)

      })
      // 处理总分和各科分数，确保正确传递
      if (this.answers[6] && Array.isArray(this.answers[6])) {
        // 先添加总分
        // myarr.unshift('总分' + this.total)

        // 确保 myarr 是正确的字符串格式而不是对象
        myarr = this.answers[6].map(item => {
          if (typeof item === 'object' && item !== null) {
            return `${item.name}:${item.score}`;
          }
          return item;
        });
      }

      // 添加总分作为单独的问题（ID为5）
      // 确保使用用户信息中的总分，而不是从输入框中获取
      this.answersInChinese['5'] = {
        questionContent: "高考总分",
        answer: this.total.toString()
      };

      // 添加ID为12题目的默认答案（就业方向题）
      // 虽然不显示该题目，但提交时需要包含默认答案
      this.answersInChinese['12'] = {
        questionContent: "就业方向",
        answer: "体制内"
      };

      for (const [key, value] of Object.entries(this.answers)) {
        const question = this.questions.find(q => q.id === parseInt(key));
        if (question) {
          // If the answer is an array (for multiple choices)
          if (Array.isArray(value)) {
            if (key === '7') {
              // 处理意向专业，使用专业名称而不是ID
              const majorNames = [];

              // 从缓存中获取专业名称
              for (const majorId of value) {
                if (this.majorNameCache && this.majorNameCache[majorId]) {
                  majorNames.push(this.majorNameCache[majorId]);
                } else {
                  // 如果缓存中没有，尝试从当前项目中查找
                  let found = false;
                  for (const item of this.items) {
                    if (item.id === majorId) {
                      majorNames.push(item.text || item.name);
                      found = true;
                      break;
                    }
                    if (item.children) {
                      for (const child of item.children) {
                        if (child.id === majorId) {
                          majorNames.push(child.text || child.name);
                          found = true;
                          break;
                        }
                        if (child.children) {
                          for (const grandChild of child.children) {
                            if (grandChild.id === majorId) {
                              majorNames.push(grandChild.text || grandChild.name);
                              found = true;
                              break;
                            }
                          }
                        }
                        if (found) break;
                      }
                    }
                    if (found) break;
                  }
                  if (!found) {
                    majorNames.push(`专业${majorId}`);
                  }
                }
              }

              // 将专业名称作为数组传递
              this.answersInChinese[key] = {
                questionContent: question.content,
                answer: majorNames // 直接传递数组，不连接成字符串
              };
            } else if (key === '6') {
              // 处理各科分数
              // 再次确保转换提取学科和分数，再将其存储为字符串
              const scoreStrings = value.map(item => {
                if (typeof item === 'object' && item !== null) {
                  return `${item.name}:${item.score}`;
                }
                return item;
              });

              this.answersInChinese[key] = {
                questionContent: question.content,
                answer: scoreStrings.join(',')
              };
            } else {
              // 处理其他多选题
              this.answersInChinese[key] = {
                questionContent: question.content,
                answer: value.map(val => {
                  const choice = question.questionChoiceDoS.find(c => c.id === val);
                  return choice ? choice.choiceContent : null;
                }).filter(Boolean)
              };
            }
          } else {
            // For single answers
            const choice = question.questionChoiceDoS.find(c => c.id === value);
            this.answersInChinese[key] = {
              questionContent: question.content,
              answer: choice ? choice.choiceContent : value
            };
          }
        }
      }
      // console.log(this.answersInChinese)

      // 设置提交状态
      this.isSubmitting = true;

      // 显示弹窗
      this.show = true;

      // 发送请求
      this.$apis.generatereport({
        userId: uni.getStorageSync('userId'),
        question: JSON.stringify(this.answersInChinese),
        version: this.ver,
        answerRecordId: this.workId ? this.workId : 0
      }).then(res => {
        // console.log('报告生成请求成功:', res);
        // 请求成功后保持弹窗显示，由用户手动关闭并返回
        // 弹窗关闭时会触发close函数，实现返回上级页面

      }).catch(err => {
        console.error('报告生成请求失败:', err);
        uni.showToast({
          title: '提交失败，请稍后再试',
          icon: 'none'
        });
        this.show = false;
        this.isSubmitting = false;
      }).finally(() => {
        // 无论成功失败，5秒后重置提交状态，允许再次提交
        setTimeout(() => {
          this.isSubmitting = false;
        }, 5000);
      })
    },
    back() {
      uni.navigateBack({
        delta: 1
      })
    },
    checkAnswerStatus(myval) {
      // 防抖机制：避免频繁调用
      if (this._checkAnswerStatusTimer) {
        clearTimeout(this._checkAnswerStatusTimer);
      }

      this._checkAnswerStatusTimer = setTimeout(() => {
        this._doCheckAnswerStatus(myval);
      }, 50);
    },

    _doCheckAnswerStatus(myval) {
      // 确保当前题目存在
      if (!this.questions || this.currentQuestion < 0 || this.currentQuestion >= this.questions.length) {
        return;
      }

      const currentQ = this.questions[this.currentQuestion];
      if (!currentQ) {
        return;
      }

      const answer = this.answers[currentQ.id];
      if (currentQ.id === 13) {
        if (!answer) {
          this.$set(this.answers, 13, '');
        }

      }
      if (currentQ.id === 7) {
        if (!answer) {
          this.$set(this.answers, 7, '');
        }
      }

      // // console.log(this.currentQuestion)
      // console.log('检查答题状态，当前答案:', answer);
      // console.log('所有答案:', this.answers);

      let isAnswered = false;

      // 根据题目ID处理特殊题目
      if (currentQ.id == 3) {
        // 选科题特殊处理 - 从用户信息中获取
        if (this.userInfo && this.userInfo.secondSubject) {
          // 如果用户信息中有选科数据，则认为已回答
          isAnswered = true;
          // console.log('选科题从用户信息中获取，设置为已回答');
        } else if (this.fkList && this.fkList.length > 0) {
          // 如果fkList中有数据，则认为已回答
          isAnswered = true;
          // console.log('从fkList中检测到选科数据');
        } else if (Array.isArray(answer) && answer.length > 0) {
          // 如果答案中有数据，则认为已回答
          isAnswered = true;
          // console.log('从答案中检测到选科数据');
        } else {
          isAnswered = false;
        }
      } else if (currentQ.id == 7) {
        // 当至少选择了一个专业时，下一题按钮变为可点击状态
        if (this.yxzy_ids && this.yxzy_ids.length > 0) {
          isAnswered = true;
        } else {
          isAnswered = false;
        }
      } else if (currentQ.id == 1 || currentQ.id == 2) {
        // 对于第1题和第2题，检查是否有答案
        isAnswered = !!answer;
      }
      // 如果还没有通过特殊题目判断确定答题状态，则根据题目类型判断
      if (!isAnswered) {
        switch (currentQ.type) {
          case 1: // 单选题
            isAnswered = !!answer;
            break;
          case 2: // 多选题
            // 对于多选题，需要检查是否有选中的选项
            if (Array.isArray(answer) && answer.length > 0) {
              isAnswered = true;
            } else {
              // 检查是否有选中的选项
              const hasCheckedChoices = currentQ.questionChoiceDoS &&
                currentQ.questionChoiceDoS.some(choice => choice.checked);
              isAnswered = hasCheckedChoices;
            }
            break;
          case 3: // 填空题
            if (currentQ.id === 13) {
              isAnswered = true; // 第13题不必填
            } else {
              isAnswered = !!answer;
            }
            break;
          default:
            isAnswered = true;
        }
      }

      // 只有当前状态为false时才更新为isAnswered
      // 这样可以避免覆盖已经设置为true的状态
      if (!this.isCurrentQuestionAnswered) {
        this.isCurrentQuestionAnswered = isAnswered;
        // console.log('当前题目答题状态更新为:', isAnswered);
      } else {
        // console.log('保持当前题目答题状态为:', this.isCurrentQuestionAnswered);
      }
      // 只有当myval为true时，才提交答案到服务器
      // 这样可以确保只在点击下一题时提交，而不是每次选择都提交
      if (myval === true) {
        console.log('checkAnswerStatus提交当前题目答案到服务器');
        console.log('当前题目ID:', this.questions[this.currentQuestion].id);
        console.log('当前题目类型:', this.questions[this.currentQuestion].type);
        console.log('当前answers对象:', this.answers);

        let arrs = []
        if (this.questions[this.currentQuestion].type == 1) {
          const currentAnswer = this.answers[this.questions[this.currentQuestion].id];
          console.log('单选题答案:', currentAnswer);
          console.log('单选题答案类型:', typeof currentAnswer);
          console.log('单选题答案是否为数组:', Array.isArray(currentAnswer));

          if (currentAnswer !== undefined && currentAnswer !== null) {
            // 如果答案是数组，取第一个元素；否则直接使用答案
            const finalAnswer = Array.isArray(currentAnswer) ? currentAnswer[0] : currentAnswer;
            console.log('处理后的答案:', finalAnswer);
            if (finalAnswer !== undefined && finalAnswer !== null) {
              arrs.push(finalAnswer);
            }
          }
        } else if (this.questions[this.currentQuestion].type == 2) {
          if (this.questions[this.currentQuestion].id == 7) {
            arrs = this.yxzy_ids
          } else {
            arrs = this.answers[this.questions[this.currentQuestion].id]
          }
        } else {
          if (this.questions[this.currentQuestion].id == 6) {
            arrs = this.fsList
          } else {
            arrs = []
          }
        }

        console.log('收集到的答案数组arrs:', arrs);
        let arr = this.answers[this.questions[this.currentQuestion].id] || []
        let newarr = []
        if (this.questions[this.currentQuestion].id == 6) {
          if (Array.isArray(arr)) {
            arr.map((item) => {
              if (item && item.name && item.score !== undefined) {
                newarr.push(item.name + ':' + item.score)
              }
            })
          }
        }
        let strs = newarr.toString()
        // console.log('提交答案到服务器，当前answerNo:', this.answerNo);

        // 确保 answerNo 是一个简单值而不是对象
        let answerNoValue = this.answerNo;

        // 记录当前answerNo值用于调试
        console.log('当前this.answerNo值:', this.answerNo);
        console.log('当前this.answerNo类型:', typeof this.answerNo);

        // 如果answerNo是undefined或null，设置为-1（生成新报告时第一题应该传递-1）
        if (answerNoValue === undefined || answerNoValue === null) {
          answerNoValue = -1;
        }

        // 保持-1值不变，这是生成新报告时第一题应该传递的值
        // 只有当answerNo是字符串'-1'时才转换为数字-1
        if (answerNoValue === '-1') {
          answerNoValue = -1;
        }

        console.log('checkAnswerStatus提交API参数:');
        console.log('- answerChoices:', this.questions[this.currentQuestion].id != 6 ? arrs : []);
        console.log('- writeContent:', this.questions[this.currentQuestion].type == 3 ? this.questions[this.currentQuestion].id == 6 ? strs : this.answers[this.questions[this.currentQuestion].id] : '');

        this.$apis.commitQuestion({
          type: this.ver,
          questionId: this.questions[this.currentQuestion].id,
          writeContent: this.questions[this.currentQuestion].type == 3 ? this.questions[this.currentQuestion].id == 6 ? strs : this.answers[this.questions[
              this.currentQuestion].id] : '',
          answerChoices: this.questions[this.currentQuestion].id != 6 ? arrs : [],
          answerNo: answerNoValue
        }).then((res) => {
          if (res.code == 0) {
            // 保存返回的答案编号
            if (typeof res.data === 'object' && res.data !== null) {
              // 直接保存answerNo的值，而不是整个对象
              this.answerNo = res.data.answerNo;
              this.workId = res.data.recordId;
              console.log('提交成功，获取到answerNo:', res.data.answerNo);
              console.log('提交成功，返回的对象数据:', res.data);
            } else {
              // 如果返回的不是对象，直接保存
              this.answerNo = res.data;
              console.log('提交成功，返回的简单数据:', res.data);
            }
            // 将answerNo保存到本地存储，以防页面刷新
            if (this.answerNo) {
              uni.setStorageSync('planQuestionnaireAnswerNo', this.answerNo);
            }
          }
        })
      } else {
        // console.log('只更新状态，不提交答案到服务器');
      }
    },
    checkAnswerStatus2() {
      // 确保当前题目存在
      if (!this.questions || this.currentQuestion < 0 || this.currentQuestion >= this.questions.length) {
        // console.log('当前题目不存在或超出范围');
        return false;
      }

      const currentQ = this.questions[this.currentQuestion];
      if (!currentQ) {
        // console.log('无法获取当前题目对象');
        return false;
      }

      const answer = currentQ.id ? this.answers[currentQ.id] : undefined;
      // console.log('检查当前题目状态，ID:', currentQ.id);
      // console.log('当前题目对象:', currentQ);
      // console.log('当前答案:', answer);

      // 特殊题目处理
      if (currentQ.id === 13 && !answer) {
        this.$set(this.answers, 13, '');
      }

      if (currentQ.id === 7 && !answer) {
        this.$set(this.answers, 7, '');
      }

      // 判断是否已答题
      let isAnswered = false;

      // 根据不同题目类型判断是否已答题
      if (currentQ.id === 3) {
        // 第3题需要选择3个科目
        isAnswered = Array.isArray(this.fkList) && this.fkList.length === 3;
      } else if (currentQ.id === 7) {
        // 第7题需要选择至少一个专业
        isAnswered = Array.isArray(this.yxzy_ids) && this.yxzy_ids.length > 0;
      } else {
        // 根据题目类型判断
        switch (currentQ.type) {
          case 1: // 单选题
            isAnswered = !!answer;
            break;
          case 2: // 多选题
            isAnswered = Array.isArray(answer) && answer.length > 0;
            break;
          case 3: // 填空题
            if (currentQ.id === 14) { // 第14题不必填
              isAnswered = true;
            } else if (currentQ.id === 6) { // 分数题
              // 判断分数是否填写完成
              const scoreAnswers = Array.isArray(this.answers[6]) ? this.answers[6] : [];
              isAnswered = scoreAnswers.length > 0 && scoreAnswers.every(item => item && item.score !== '');
            } else {
              isAnswered = !!answer;
            }
            break;
          default:
            isAnswered = true;
        }
      }

      // 记录当前题目答题状态
      this.isCurrentQuestionAnswered = isAnswered;
      // console.log('当前题目答题状态:', isAnswered);

      // 准备提交的答案数组
      let arrs = [];

      // 根据题目类型收集答案
      if (currentQ.type === 1) { // 单选题
        const currentAnswer = this.answers[currentQ.id];
        if (currentAnswer !== undefined && currentAnswer !== null) {
          arrs.push(currentAnswer);
          console.log('单选题答案收集:', currentAnswer, '题目ID:', currentQ.id);
        } else {
          console.log('单选题答案为空:', currentAnswer, '题目ID:', currentQ.id);
        }
      } else if (currentQ.type === 2) { // 多选题
        if (currentQ.id === 7) { // 第7题，专业选择
          arrs = Array.isArray(this.yxzy_ids) ? this.yxzy_ids : [];
          console.log('专业选择答案:', arrs);
        } else if (currentQ.id === 3) { // 第3题，选科题
          arrs = Array.isArray(this.fkList) ? this.fkList : [];
          console.log('选科题答案:', arrs);
        } else {
          arrs = Array.isArray(this.answers[currentQ.id]) ? this.answers[currentQ.id] : [];
          console.log('其他多选题答案:', arrs);
        }
      } else { // 填空题或其他类型
        if (this.questions[this.currentQuestion].id == 6) {
          arrs = this.fsList
        } else {
          arrs = []
        }
      }
      // 安全获取当前题目的答案
      let arr = this.answers[this.questions[this.currentQuestion]?.id] || []
      let newarr = []

      // 如果是分数题(第6题)
      if (this.questions[this.currentQuestion]?.id == 6) {
        // 验证分数总和是否等于总分
        this.validateScoreSum();

        // 生成分数展示数组 - 确保 arr 存在且是数组
        if (Array.isArray(arr)) {
          arr.forEach((item) => {
            if (item && item.name && item.score !== undefined) {
              newarr.push(item.name + ':' + item.score)
            }
          })
        }
      }
      if (currentQ.id === 1 && answer) {
        if (answer == 11) {
          this.questions[2].questionChoiceDoS = [{
            "createTime": 1742905924000,
            "updateTime": 1742905924000,
            "creator": "admin",
            "updater": "admin",
            "deleted": false,
            "id": 34,
            "questionId": 3,
            "choiceContent": "物理",
            "sort": 1,
            "type": 1,
            "imageUrl": null
          }, {
            "createTime": 1742905924000,
            "updateTime": 1742905924000,
            "creator": "admin",
            "updater": "admin",
            "deleted": false,
            "id": 35,
            "questionId": 3,
            "choiceContent": "化学",
            "sort": 1,
            "type": 1,
            "imageUrl": null
          }, {
            "createTime": 1742905924000,
            "updateTime": 1742905924000,
            "creator": "admin",
            "updater": "admin",
            "deleted": false,
            "id": 36,
            "questionId": 3,
            "choiceContent": "生物",
            "sort": 1,
            "type": 1,
            "imageUrl": null
          }, {
            "createTime": 1742905924000,
            "updateTime": 1742905924000,
            "creator": "admin",
            "updater": "admin",
            "deleted": false,
            "id": 37,
            "questionId": 3,
            "choiceContent": "政治",
            "sort": 1,
            "type": 1,
            "imageUrl": null
          }, {
            "createTime": 1742905924000,
            "updateTime": 1742905924000,
            "creator": "admin",
            "updater": "admin",
            "deleted": false,
            "id": 38,
            "questionId": 3,
            "choiceContent": "历史",
            "sort": 1,
            "type": 1,
            "imageUrl": null
          }, {
            "createTime": 1742905924000,
            "updateTime": 1742905924000,
            "creator": "admin",
            "updater": "admin",
            "deleted": false,
            "id": 39,
            "questionId": 3,
            "choiceContent": "地理",
            "sort": 1,
            "type": 1,
            "imageUrl": null
          }, {
            "createTime": 1742905924000,
            "updateTime": 1742905924000,
            "creator": "admin",
            "updater": "admin",
            "deleted": false,
            "id": 40,
            "questionId": 3,
            "choiceContent": "技术",
            "sort": 1,
            "type": 1,
            "imageUrl": null
          }]
        } else {
          this.questions[2].questionChoiceDoS = [{
            "createTime": 1742905924000,
            "updateTime": 1742905924000,
            "creator": "admin",
            "updater": "admin",
            "deleted": false,
            "id": 34,
            "questionId": 3,
            "choiceContent": "物理",
            "sort": 1,
            "type": 1,
            "imageUrl": null
          }, {
            "createTime": 1742905924000,
            "updateTime": 1742905924000,
            "creator": "admin",
            "updater": "admin",
            "deleted": false,
            "id": 35,
            "questionId": 3,
            "choiceContent": "化学",
            "sort": 1,
            "type": 1,
            "imageUrl": null
          }, {
            "createTime": 1742905924000,
            "updateTime": 1742905924000,
            "creator": "admin",
            "updater": "admin",
            "deleted": false,
            "id": 36,
            "questionId": 3,
            "choiceContent": "生物",
            "sort": 1,
            "type": 1,
            "imageUrl": null
          }, {
            "createTime": 1742905924000,
            "updateTime": 1742905924000,
            "creator": "admin",
            "updater": "admin",
            "deleted": false,
            "id": 37,
            "questionId": 3,
            "choiceContent": "政治",
            "sort": 1,
            "type": 1,
            "imageUrl": null
          }, {
            "createTime": 1742905924000,
            "updateTime": 1742905924000,
            "creator": "admin",
            "updater": "admin",
            "deleted": false,
            "id": 38,
            "questionId": 3,
            "choiceContent": "历史",
            "sort": 1,
            "type": 1,
            "imageUrl": null
          }, {
            "createTime": 1742905924000,
            "updateTime": 1742905924000,
            "creator": "admin",
            "updater": "admin",
            "deleted": false,
            "id": 39,
            "questionId": 3,
            "choiceContent": "地理",
            "sort": 1,
            "type": 1,
            "imageUrl": null
          }]
        }
      }
      // 检查当前问题是否为第三题
      if (currentQ.id === 3 && Array.isArray(answer)) {
        // 将选择的科目带入下一题的各科分数中
        const defaultSubjects = [{
          name: '语文',
          score: ''
        },
          {
            name: '数学',
            score: ''
          },
          {
            name: '英语',
            score: ''
          }
        ];

        const selectedSubjects = answer.map(subjectId => {
          const choice = currentQ.questionChoiceDoS.find(c => c.id === subjectId);
          return {
            name: choice ? choice.choiceContent : '',
            score: ''
          };
        });

        // 合并默认科目和选择的科目
        const subjects = defaultSubjects.concat(selectedSubjects);

        // 初始化下一题的科目分数
        const nextQ = this.questions.find(q => q.id === 6); // 假设下一题的ID为6
        if (nextQ) {
          nextQ.subjects = subjects;
        }
      }


      // if (currentQ.id == 3 && answer.length == 3 || answer) {
      // 	this.currentQuestion = this.currentQuestion + 1
      // }


      let strs = newarr.toString()
      // 确保 answerNo 是一个简单值而不是对象
      let answerNoValue = typeof this.answerNo === 'object' && this.answerNo !== null && this.answerNo.answerNo !== undefined
        ? this.answerNo.answerNo
        : this.answerNo;

      // 如果answerNo仍然是-1或者无效值，对于第一题和第二题，传递空字符串或null
      if (answerNoValue === -1 || answerNoValue === '-1') {
        answerNoValue = '';
      }

      console.log('提交答案到服务器:');
      console.log('- 题目ID:', this.questions[this.currentQuestion].id);
      console.log('- 题目内容:', this.questions[this.currentQuestion].content);
      console.log('- 题目类型:', this.questions[this.currentQuestion].type);
      console.log('- 当前answers对象:', this.answers);
      console.log('- 当前题目答案:', this.answers[this.questions[this.currentQuestion].id]);
      console.log('- 收集到的arrs:', arrs);
      console.log('- answerChoices:', this.questions[this.currentQuestion].id != 6 ? arrs : []);
      console.log('- writeContent:', this.questions[this.currentQuestion].type == 3 ? this.questions[this.currentQuestion].id == 6 ? strs : this.answers[this.questions[this.currentQuestion].id] : '');
      console.log('- answerNo:', answerNoValue);

      this.$apis.commitQuestion({
        type: this.ver,
        questionId: this.questions[this.currentQuestion].id,
        writeContent: this.questions[this.currentQuestion].type == 3 ? this.questions[this.currentQuestion].id == 6 ? strs : this.answers[this.questions[
            this.currentQuestion].id] : '',
        answerChoices: this.questions[this.currentQuestion].id != 6 ? arrs : [],
        answerNo: answerNoValue
      }).then((res) => {
        if (res.code == 0) {
          // 保存返回的 answerNo 和 recordId
          if (typeof res.data === 'object' && res.data !== null) {
            this.answerNo = res.data.answerNo;
            this.workId = res.data.recordId;
            // console.log('服务器返回的对象数据:', res.data);
          } else {
            // 如果返回的不是对象，直接保存
            this.answerNo = res.data;
            // console.log('服务器返回的简单数据:', res.data);
          }
          this.checkAnswerStatus()
          if (this.currentQuestion != 13) {
            this.isCurrentQuestionAnswered = false
          }

          // 找到就业方向题目的索引
          const jobDirectionIndex = this.questions.findIndex(q => q.id === 12);
          // console.log(`checkAnswerStatus2 中: 就业方向题索引=${jobDirectionIndex}, 当前题目索引=${this.currentQuestion}`);

          // 如果下一题是第12题（就业方向），则跳过它
          if (this.currentQuestion === jobDirectionIndex - 1) { // 当前题目是就业方向题的前一题
            // console.log('检测到下一题是就业方向题，将跳过');
            // 跳过就业方向题，直接到下下题
            if (jobDirectionIndex + 1 < this.questions.length) {
              this.currentQuestion = jobDirectionIndex + 1;
              // console.log(`跳过就业方向题，直接到索引=${this.currentQuestion}`);
            } else {
              // 如果没有下下题，则只前进一题
              this.currentQuestion = this.currentQuestion + 1;
              // console.log(`前进到索引=${this.currentQuestion}`);
            }
          } else if (this.currentQuestion === jobDirectionIndex) {
            // 如果当前题目就是就业方向题，也跳过
            // console.log('当前题目是就业方向题，将跳过');
            if (jobDirectionIndex + 1 < this.questions.length) {
              this.currentQuestion = jobDirectionIndex + 1;
              // console.log(`跳过就业方向题，直接到索引=${this.currentQuestion}`);
            }
          } else {
            // 正常前进到下一题
            this.currentQuestion = this.currentQuestion + 1;
            // console.log(`正常前进到索引=${this.currentQuestion}`);
          }

          uni.pageScrollTo({
            scrollTop: 0,
            duration: 100
          })
        }
      })

    },
    selectMajor(item) {
      // 处理专业选择/取消选择
      if (this.yxzy_ids.includes(item.id)) {
        // 如果已经选中，则取消选择
        const index = this.yxzy_ids.indexOf(item.id);
        this.yxzy_ids.splice(index, 1);
      } else {
        // 如果未选中且未达到最大选择数，则选中
        if (this.yxzy_ids.length < 3) { // 修改为最多选择3个专业
          this.yxzy_ids.push(item.id);
        } else {
          uni.showToast({
            title: '只能选择1个专业', // 修改提示文本
            icon: 'none',
            duration: 2000
          });
          return;
        }
      }
      // 更新答案并检查状态
      this.$set(this.answers, 7, this.yxzy_ids);
      this.checkAnswerStatus();
    },

    // 检查ID是否在数组中，使用与组件相同的方法
    isIdSelected(itemId) {
      // 如果数组为空，直接返回false
      if (!this.yxzy_ids || this.yxzy_ids.length === 0) {
        return false;
      }

      // 使用与组件相同的方法检查ID是否在数组中
      return this.yxzy_ids.indexOf(itemId) !== -1;
    },

    // 新增的调试方法，用于跟踪专业选择状态
    debugMajorSelection(message) {
      // console.log('===== 专业选择调试 (' + message + ') =====');
      // console.log('当前选中的专业 IDs:', JSON.stringify(this.yxzy_ids));
      if (this.$refs.treeSelect) {
        // console.log('组件的 activeIds:', JSON.stringify(this.$refs.treeSelect.active_ids));
      }
      // console.log('当前答案:', JSON.stringify(this.answers[7]));
      // console.log('按钮状态:', this.isCurrentQuestionAnswered);
      // console.log('===============================');
    },

    // 直接处理专业选择的方法
    directSelectMajor(item) {
      // 防止重复点击
      if (this.isProcessingSelection) {
        return;
      }
      this.isProcessingSelection = true;

      try {
        // 先强制初始化 yxzy_ids 数组
        if (!this.yxzy_ids) {
          this.yxzy_ids = [];
        }

        // 缓存专业名称，以便在切换标签页后仍能显示
        if (!this.majorNameCache) this.majorNameCache = {};
        this.majorNameCache[item.id] = item.text || item.name;

        // 使用原始的item.id，不进行类型转换
        const itemId = item.id;

        // 检查是否已经选中
        const index = this.yxzy_ids.indexOf(itemId);
        const isSelected = index !== -1;

        if (isSelected) {
          // 如果已经选中，则取消选择
          this.yxzy_ids.splice(index, 1);
          uni.showToast({
            title: '已取消选择: ' + (item.text || item.name),
            icon: 'none',
            duration: 1000
          });
        } else {
          // 如果未选中，则选中（只能选择一个专业）
          // 检查是否已经选择了其他专业
          const hasSelectedOther = this.yxzy_ids.length > 0;
          const oldMajorName = hasSelectedOther ? this.getMajorNameById(this.yxzy_ids[0]) : '';

          // 先清空已选专业
          this.yxzy_ids = [];
          // 添加新选择的专业
          this.yxzy_ids.push(itemId);

          // 如果之前已经选择了专业，显示替换提示
          if (hasSelectedOther) {
            uni.showToast({
              title: '已替换为: ' + (item.text || item.name),
              icon: 'none',
              duration: 1500
            });
          } else {
            uni.showToast({
              title: '已选择: ' + (item.text || item.name),
              icon: 'none',
              duration: 1000
            });
          }
        }

        // 更新答案并检查状态
        this.$set(this.answers, 7, this.yxzy_ids);
        this.checkAnswerStatus();

      } finally {
        // 延时解除锁定，确保操作完成
        setTimeout(() => {
          this.isProcessingSelection = false;
        }, 300);
      }
    },

    handleItemClick(ret) {
      // console.log('专业选择触发:', ret);
      this.debugMajorSelection('点击前');

      // 缓存专业名称，以便在切换标签页后仍能显示
      if (ret && ret.id) {
        if (!this.majorNameCache) this.majorNameCache = {};
        this.majorNameCache[ret.id] = ret.text || ret.name;

        // 手动更新yxzy_ids数组
        if (this.yxzy_ids.includes(ret.id)) {
          // 如果已经选中，则取消选择
          const index = this.yxzy_ids.indexOf(ret.id);
          this.yxzy_ids.splice(index, 1);
        } else {
          // 如果未选中，则选中（只能选择一个专业）
          // 检查是否已经选择了其他专业
          const hasSelectedOther = this.yxzy_ids.length > 0;
          const oldMajorName = hasSelectedOther ? this.getMajorNameById(this.yxzy_ids[0]) : '';

          // 先清空已选专业
          this.yxzy_ids = [];
          // 添加新选择的专业
          this.yxzy_ids.push(ret.id);

          // 如果之前已经选择了专业，显示替换提示
          if (hasSelectedOther) {
            uni.showToast({
              title: '暂只支持选择一个意向专业\n' + oldMajorName + ' 已替换为 ' + ret.text,
              icon: 'none',
              duration: 2000
            });
          } else {
            uni.showToast({
              title: '已选择: ' + ret.text,
              icon: 'none',
              duration: 1000
            });
          }
        }
      }

      // 直接更新答案并检查状态
      this.$set(this.answers, 7, this.yxzy_ids);
      this.checkAnswerStatus();
      this.debugMajorSelection('点击后');
    },
    // 处理左侧导航点击事件
    handleCategoryClick(index) {
      // 防止重复点击
      if (this.isProcessingSelection) {
        return;
      }

      // 获取选中的类别
      const selectedCategory = this.items[index];

      // 如果该类别尚未加载数据或者children为null，则加载数据
      if (selectedCategory && (!selectedCategory.loaded || !selectedCategory.children || selectedCategory.children.length === 0)) {
        this.getSubject(selectedCategory.id);
      }
    },

    // 处理树形组件选择事件
    onItem(ret) {
      // console.log('树形组件选择回调:', ret);

      if (!ret || !ret.item) {
        // console.error('无效的选择项数据');
        return;
      }

      const item = ret.item;

      // 检查是否第三级专业项（没有children的项）
      if (item && !item.children) {
        // 这是专业项，处理选择
        this.directSelectMajor(item);
      } else if (item && item.children && item.children.length > 0) {
        // 如果是有子项的类别，只展开不选择
        // console.log('打开类别:', item.text, '包含', item.children.length, '个子项');
      }
    },

    bindChange(ids, key, popupRef) {
      // console.log('组件触发 onChange 事件:', ids, key, popupRef);
      this.debugMajorSelection('bindChange 前');

      this[key] = ids;
      this.$set(this.answers, 7, this[key]);
      this.checkAnswerStatus();

      this.debugMajorSelection('bindChange 后');

      if (popupRef) {
        if (this.$refs[popupRef] && this.$refs[popupRef].close) {
          this.$refs[popupRef].close();
        } else {
          // console.log('popupRef 不存在或没有 close 方法');
        }
      }
    },

    // Province selection is now disabled and set from user info
    clickProvince(item, id) {
      // Return early as province selection is now fixed
      return;

      if (id == 11) {
        this.questions[2].questionChoiceDoS = [{
          "createTime": 1742905924000,
          "updateTime": 1742905924000,
          "creator": "admin",
          "updater": "admin",
          "deleted": false,
          "id": 34,
          "questionId": 3,
          "choiceContent": "物理",
          "sort": 1,
          "type": 1,
          "imageUrl": null
        }, {
          "createTime": 1742905924000,
          "updateTime": 1742905924000,
          "creator": "admin",
          "updater": "admin",
          "deleted": false,
          "id": 35,
          "questionId": 3,
          "choiceContent": "化学",
          "sort": 1,
          "type": 1,
          "imageUrl": null
        }, {
          "createTime": 1742905924000,
          "updateTime": 1742905924000,
          "creator": "admin",
          "updater": "admin",
          "deleted": false,
          "id": 36,
          "questionId": 3,
          "choiceContent": "生物",
          "sort": 1,
          "type": 1,
          "imageUrl": null
        }, {
          "createTime": 1742905924000,
          "updateTime": 1742905924000,
          "creator": "admin",
          "updater": "admin",
          "deleted": false,
          "id": 37,
          "questionId": 3,
          "choiceContent": "政治",
          "sort": 1,
          "type": 1,
          "imageUrl": null
        }, {
          "createTime": 1742905924000,
          "updateTime": 1742905924000,
          "creator": "admin",
          "updater": "admin",
          "deleted": false,
          "id": 38,
          "questionId": 3,
          "choiceContent": "历史",
          "sort": 1,
          "type": 1,
          "imageUrl": null
        }, {
          "createTime": 1742905924000,
          "updateTime": 1742905924000,
          "creator": "admin",
          "updater": "admin",
          "deleted": false,
          "id": 39,
          "questionId": 3,
          "choiceContent": "地理",
          "sort": 1,
          "type": 1,
          "imageUrl": null
        }, {
          "createTime": 1742905924000,
          "updateTime": 1742905924000,
          "creator": "admin",
          "updater": "admin",
          "deleted": false,
          "id": 40,
          "questionId": 3,
          "choiceContent": "技术",
          "sort": 1,
          "type": 1,
          "imageUrl": null
        }]
      } else {
        this.questions[2].questionChoiceDoS = [{
          "createTime": 1742905924000,
          "updateTime": 1742905924000,
          "creator": "admin",
          "updater": "admin",
          "deleted": false,
          "id": 34,
          "questionId": 3,
          "choiceContent": "物理",
          "sort": 1,
          "type": 1,
          "imageUrl": null
        }, {
          "createTime": 1742905924000,
          "updateTime": 1742905924000,
          "creator": "admin",
          "updater": "admin",
          "deleted": false,
          "id": 35,
          "questionId": 3,
          "choiceContent": "化学",
          "sort": 1,
          "type": 1,
          "imageUrl": null
        }, {
          "createTime": 1742905924000,
          "updateTime": 1742905924000,
          "creator": "admin",
          "updater": "admin",
          "deleted": false,
          "id": 36,
          "questionId": 3,
          "choiceContent": "生物",
          "sort": 1,
          "type": 1,
          "imageUrl": null
        }, {
          "createTime": 1742905924000,
          "updateTime": 1742905924000,
          "creator": "admin",
          "updater": "admin",
          "deleted": false,
          "id": 37,
          "questionId": 3,
          "choiceContent": "政治",
          "sort": 1,
          "type": 1,
          "imageUrl": null
        }, {
          "createTime": 1742905924000,
          "updateTime": 1742905924000,
          "creator": "admin",
          "updater": "admin",
          "deleted": false,
          "id": 38,
          "questionId": 3,
          "choiceContent": "历史",
          "sort": 1,
          "type": 1,
          "imageUrl": null
        }, {
          "createTime": 1742905924000,
          "updateTime": 1742905924000,
          "creator": "admin",
          "updater": "admin",
          "deleted": false,
          "id": 39,
          "questionId": 3,
          "choiceContent": "地理",
          "sort": 1,
          "type": 1,
          "imageUrl": null
        }]
      }

      // 更新第三题选项
      // const techOption = this.questions[2].questionChoiceDoS.find(item => item.provinceId === id);
      // if (techOption) {
      //     // 如果选择的是浙江，显示技术选项
      //     techOption.hidden = false;
      // } else {
      //     // 如果选择的是其他省份，隐藏技术选项
      //     const techOption = this.questions[2].questionChoiceDoS.find(item => item.provinceId === 11);
      //     if (techOption) {
      //         techOption.hidden = true;
      //     }
      // }

      // 修改为Vue的响应式数据更新方式
      this.$set(this.answers, id, item);

      // 先设置状态为已回答，但不提交答案
      this.isCurrentQuestionAnswered = true;
      // 强制刷新视图
      this.$forceUpdate();
      // 只更新状态，不提交答案
      this.checkAnswerStatus(false);
      // console.log('省份选择已更新，答题状态:', this.isCurrentQuestionAnswered);

      return this.questions
    },
    clickSex(item, id) {
      console.log('性别选择：', item, id);

      // 修改为Vue的响应式数据更新方式
      this.$set(this.answers, id, item);

      console.log('性别选择后的answers:', this.answers);
      console.log('当前answers[' + id + ']:', this.answers[id]);

      // 先设置状态为已回答，但不提交答案
      this.isCurrentQuestionAnswered = true;
      // 只更新状态，不提交答案
      this.checkAnswerStatus(false);
      console.log('性别选择已更新，答题状态:', this.isCurrentQuestionAnswered);
    },
    // 处理其他单选题选择
    handleSelect(e) {
      const {
        questionId
      } = e.currentTarget.dataset;
      const {
        id
      } = e.currentTarget.dataset;

      // console.log('单选题选择：', questionId, id);

      // 修改为Vue的响应式数据更新方式
      this.$set(this.answers, questionId, id);

      // 先设置状态为已回答，但不提交答案
      this.isCurrentQuestionAnswered = true;
      // 只更新状态，不提交答案
      this.checkAnswerStatus(false);
      // console.log('单选题选择已更新，答题状态:', this.isCurrentQuestionAnswered);
    },
    handleInput(e) {
      const questionId = e.currentTarget.dataset.questionId;
      // 如果是高考总分题（ID为5），不处理输入
      if (questionId === 5 || parseInt(questionId) === 5) {
        return;
      }

      let value = e.detail.value;

      // 如果是ID为13的题目，进行特殊输入限制
      if (questionId === 13 || parseInt(questionId) === 13) {
        value = this.validateQuestion13Input(value);
        // 如果验证失败，不更新答案
        if (value === null) {
          return;
        }
      }

      // 更新答案
      this.$set(this.answers, questionId, value);

      // 先设置状态为已回答，但不提交答案
      this.isCurrentQuestionAnswered = true;
      // 只更新状态，不提交答案
      this.checkAnswerStatus(false);

      // 如果是ID为13的题目且有有效输入，自动进入下一题
      if ((questionId === 13 || parseInt(questionId) === 13) && value && value.trim() !== '') {
        setTimeout(() => {
          this.nextQuestion();
        }, 300);
      }
    },

    // 验证ID为13的题目输入
    validateQuestion13Input(value) {
      // 限制长度为15个字符
      if (value.length > 15) {
        uni.showToast({
          title: '最多只能输入15个字符',
          icon: 'none',
          duration: 2000
        });
        return value.substring(0, 15);
      }

      // 只允许中文字符，移除所有非中文字符
      const chineseOnly = /[\u4e00-\u9fa5]/g;
      const filteredValue = value.match(chineseOnly);
      const cleanValue = filteredValue ? filteredValue.join('') : '';

      // 如果过滤后的值与原值不同，说明包含了非中文字符
      if (cleanValue !== value && value.length > 0) {
        uni.showToast({
          title: '只能输入中文字符',
          icon: 'none',
          duration: 2000
        });
      }

      // 检查违禁字
      const forbiddenWords = this.getForbiddenWords();
      let finalValue = cleanValue;
      for (let word of forbiddenWords) {
        if (finalValue.includes(word)) {
          uni.showToast({
            title: '输入内容包含违禁字',
            icon: 'none',
            duration: 2000
          });
          finalValue = finalValue.replace(new RegExp(word, 'g'), '');
        }
      }

      return finalValue;
    },

    // 获取违禁字列表
    getForbiddenWords() {
      return [
        '政府', '党', '习近平', '毛泽东', '共产党', '国家主席', '总书记',
        '政治', '革命', '反动', '颠覆', '暴动', '起义', '造反',
        '黄色', '色情', '淫秽', '赌博', '毒品', '吸毒', '贩毒',
        '杀人', '自杀', '爆炸', '恐怖', '暴力', '血腥',
        '法轮功', '邪教', '迷信', '占卜', '算命',
        '台独', '藏独', '疆独', '港独', '分裂', '独立',
        '民主', '自由', '人权', '抗议', '游行', '示威',
        '六四', '天安门', '文革', '大跃进', '三年自然灾害'
      ];
    },
    handleSubjectScore(e) {
      const {
        questionId,
        subjectIndex
      } = e.currentTarget.dataset;
      const {
        value
      } = e.detail;

      // 将索引转为数字类型
      const index = Number(subjectIndex);
      // console.log('分数输入:', questionId, index, value);

      // 验证分数是否为数字且在0-150之间
      const score = Number(value);
      if (isNaN(score) || score < 0 || score > 150) {
        uni.showToast({
          title: '请输入0-150之间的数字',
          icon: 'none'
        });
        return;
      }

      // 检查subjects数组是否初始化
      if (!this.subjects || !Array.isArray(this.subjects)) {
        console.error('科目数组未初始化');
        return;
      }

      // 检查索引是否有效
      if (index < 0 || index >= this.subjects.length) {
        console.error('索引超出范围:', index, this.subjects.length);
        return;
      }

      // 获取当前科目名称
      const currentSubjectName = this.subjects[index].name;
      // console.log('正在修改科目:', currentSubjectName, '分数:', value);

      // 创建新对象更新当前科目，避免引用问题
      const updatedSubject = {
        name: currentSubjectName,
        score: value
      };

      // 更新subjects数组
      this.$set(this.subjects, index, updatedSubject);

      // 确保第6题的答案数组已初始化
      if (!this.answers[6] || !Array.isArray(this.answers[6])) {
        // 深拷贝创建新数组
        const initialSubjects = this.subjects.map(s => ({
          name: s.name,
          score: s.score || ''
        }));
        this.$set(this.answers, 6, initialSubjects);
      }

      // 更新答案数组中当前科目的分数
      if (index < this.answers[6].length) {
        const updatedAnswerSubject = {
          name: currentSubjectName,
          score: value
        };
        this.$set(this.answers[6], index, updatedAnswerSubject);
      }

      // 验证总分
      this.validateScoreSum();
    },
    // 新的科目分数处理函数，使用独立的ID完全避免互相影响
    handleSubjectScoreNew(e, subjectId, subjectName) {
      const value = e.detail.value;
      // console.log('分数输入(新):', subjectId, subjectName, value);

      // 验证分数是否为数字且在0-150之间
      const score = Number(value);
      if (isNaN(score) || score < 0 || score > 150) {
        uni.showToast({
          title: '请输入0-150之间的数字',
          icon: 'none'
        });

        // 获取当前有效值
        const currentValue = this.subjectScores[subjectId] || '';

        // 立即修改输入框的值
        // 在微信小程序中，我们需要使用特殊方法来重置输入框的值
        // 这里我们使用一个延迟执行的方法，确保在当前事件循环结束后执行
        setTimeout(() => {
          // 1. 先清空值
          this.$set(this.subjectScores, subjectId, '');
          this.$forceUpdate();

          // 2. 再设置回原来的有效值
          setTimeout(() => {
            this.$set(this.subjectScores, subjectId, currentValue);
            this.$forceUpdate();
          }, 10);
        }, 0);

        return;
      }

      // 直接更新独立存储对象中的分数
      this.$set(this.subjectScores, subjectId, value);
      // console.log('已更新 subjectScores:', JSON.stringify(this.subjectScores));

      // 因为我们仍然需要将分数存储到answers[6]中以便提交
      // 我们确保在必要时初始化它
      if (!this.answers[6] || !Array.isArray(this.answers[6])) {
        // 初始化空科目数组
        this.$set(this.answers, 6, []);
      }

      // 查找该科目是否已存在于answers中
      const subjectIndex = this.answers[6].findIndex(s => s.name === subjectName);

      if (subjectIndex >= 0) {
        // 更新现有科目
        const updatedSubject = {
          name: subjectName,
          score: value
        };
        this.$set(this.answers[6], subjectIndex, updatedSubject);
      } else {
        // 添加新科目
        this.answers[6].push({
          name: subjectName,
          score: value
        });
      }
      // console.log('已更新 answers[6]:', JSON.stringify(this.answers[6]));

      // 更新fsList数组，用于控制下一题按钮
      this.updateFsList();
      // console.log('已更新 fsList:', JSON.stringify(this.fsList));

      // 验证总分
      this.validateScoreSum();

      // 更新当前题目的答题状态
      this.updateCurrentQuestionAnsweredStatus();
    },
    // 更新fsList数组
    updateFsList() {
      // 清空fsList
      this.fsList = [];

      // 遍历所有已录入的分数
      Object.entries(this.subjectScores).forEach(([subjectId, score]) => {
        if (score && score.trim() !== '') {
          // 根据科目编码获取科目名称
          let subjectName = '';
          switch(subjectId) {
            case 'chinese': subjectName = '语文'; break;
            case 'math': subjectName = '数学'; break;
            case 'english': subjectName = '英语'; break;
            case 'physics': subjectName = '物理'; break;
            case 'chemistry': subjectName = '化学'; break;
            case 'biology': subjectName = '生物'; break;
            case 'politics': subjectName = '政治'; break;
            case 'history': subjectName = '历史'; break;
            case 'geography': subjectName = '地理'; break;
            case 'tech': subjectName = '技术'; break;
            default: subjectName = subjectId;
          }

          // 添加到fsList中
          this.fsList.push(subjectName + ':' + score);
        }
      });

      // console.log('当前fsList:', this.fsList);
    },
    validateScoreSum() {
      if (!this.total) {
        // console.log('总分不存在');
        this.scoreSumError = false; // 设置分数错误状态为false
        return true; // 如果没有总分，先不验证
      }

      // 使用独立存储对象计算总分
      const currentSum = this.getCurrentSum();

      // console.log('当前科目分数之和:', currentSum, '目标总分:', this.total);

      // 检查是否有任何科目分数已填写
      const hasAnyScores = Object.keys(this.subjectScores).length > 0;

      // 计算当前总分与目标总分的差值，但不显示提示
      if (hasAnyScores && Math.abs(currentSum - this.total) > 0.1) {
        // 移除提示，只设置错误状态
        this.scoreSumError = true; // 设置分数错误状态为true
        return false;
      }

      this.scoreSumError = false; // 设置分数错误状态为false
      return true;
    },
    getCurrentSum() {
      let currentSum = 0;

      // 遍历所有科目分数
      Object.values(this.subjectScores).forEach(scoreStr => {
        const score = Number(scoreStr) || 0;
        currentSum += score;
      });

      return currentSum;
    },

    getDifference() {
      const currentSum = this.getCurrentSum();
      return (this.total || 0) - currentSum;
    },

    // 计算科目总分
    calculateTotalScore() {
      // 使用现有的getCurrentSum方法计算当前分数总和
      const currentSum = this.getCurrentSum();
      // console.log('计算的总分:', currentSum);

      // 如果还没有设置总分，则设置它
      if (!this.total && currentSum > 0) {
        this.total = currentSum;
      }

      return currentSum;
    },

    // 检查选科题的状态
    checkSubjectSelectionStatus() {
      // 找到选科题
      const subjectQuestion = this.questions.find(q => q.id === 3);
      if (!subjectQuestion) {
        // console.log('未找到选科题');
        return;
      }

      // 检查选科题的选项状态
      const checkedOptions = subjectQuestion.questionChoiceDoS.filter(item => item.checked);
      // console.log('选科题的选中选项：', checkedOptions.map(item => item.choiceContent));

      // 检查答案数组
      const answerIds = this.answers[3];
      // console.log('选科题的答案数组：', answerIds);

      // 如果有答案数组，但选项没有正确显示为已选中，则根据答案数组设置选中状态
      if (Array.isArray(answerIds) && answerIds.length > 0) {
        // console.log('根据答案数组设置选中状态');

        // 先深拷贝选项数组，避免引用问题
        const choicesCopy = JSON.parse(JSON.stringify(subjectQuestion.questionChoiceDoS));

        // 先重置所有选项的选中状态
        choicesCopy.forEach(choice => {
          choice.checked = false;
        });

        // 然后将选中的选项标记为已选中
        choicesCopy.forEach(choice => {
          if (answerIds.includes(choice.id)) {
            choice.checked = true;
            // console.log(`将选项 ${choice.choiceContent} 标记为已选中`);
          }
        });

        // 使用Vue的响应式更新整个数组
        this.$set(subjectQuestion, 'questionChoiceDoS', choicesCopy);

        // 更新当前题目的答题状态
        if (this.currentQuestion === 2) { // 如果当前题目就是选科题
          this.updateCurrentQuestionAnsweredStatus();
        }

        return; // 已经处理完毕，直接返回
      }

      // 如果没有选中的选项，但用户信息中有选科数据，则重新设置选中状态
      if (checkedOptions.length === 0 && this.userInfo && this.userInfo.secondSubject) {
        // console.log('选科题没有选中选项，但用户信息中有选科数据，重新设置选中状态');

        // 处理secondSubject字段，可能是逗号分隔的字符串
        let subjectsData;
        if (typeof this.userInfo.secondSubject === 'string') {
          // 将逗号分隔的字符串转换为数组
          subjectsData = this.userInfo.secondSubject.split(',').map(item => item.trim());
        } else if (Array.isArray(this.userInfo.secondSubject)) {
          subjectsData = this.userInfo.secondSubject;
        } else {
          subjectsData = [];
        }

        // 对每个用户选科，找到对应的选项ID
        const selectedIds = [];
        subjectsData.forEach(subjectName => {
          const choice = subjectQuestion.questionChoiceDoS.find(c =>
            c.choiceContent === subjectName);
          if (choice) {
            selectedIds.push(choice.id);
            // console.log(`找到选科 ${subjectName} 的ID: ${choice.id}`);
          } else {
            // console.log(`未找到选科 ${subjectName} 对应的选项`);
          }
        });

        if (selectedIds.length > 0) {
          // 先深拷贝选项数组，避免引用问题
          const choicesCopy = JSON.parse(JSON.stringify(subjectQuestion.questionChoiceDoS));

          // 先重置所有选项的选中状态
          choicesCopy.forEach(choice => {
            choice.checked = false;
          });

          // 然后将选中的选项标记为已选中
          choicesCopy.forEach(choice => {
            if (selectedIds.includes(choice.id)) {
              choice.checked = true;
              // console.log(`将选项 ${choice.choiceContent} 标记为已选中`);
            }
          });

          // 使用Vue的响应式更新整个数组
          this.$set(subjectQuestion, 'questionChoiceDoS', choicesCopy);

          // 设置选科答案
          this.$set(this.answers, 3, selectedIds);
          this.fkList = selectedIds;

          // 更新当前题目的答题状态
          if (this.currentQuestion === 2) { // 如果当前题目就是选科题
            this.updateCurrentQuestionAnsweredStatus();
          }
        }
      }
    },

    // 获取用户选择的科目列表
    getSelectedSubjects() {
      // console.log('当前 fkList:', this.fkList);
      // console.log('当前 subjectScores:', this.subjectScores);

      // 相关常量定义
      const CORE_SUBJECTS = [
        {id: 'chinese', name: '语文'},
        {id: 'math', name: '数学'},
        {id: 'english', name: '英语'}
      ];

      // 默认情况下三个必选科目始终存在
      const selectedSubjects = [...CORE_SUBJECTS];

      // 如果有 subjectScores 数据，优先使用它来生成科目列表
      if (this.subjectScores && Object.keys(this.subjectScores).length > 0) {
        // console.log('使用 subjectScores 生成科目列表');

        // 初始化结果数组
        const subjects = [];
        const existingSubjectIds = new Set();

        // 添加必选科目
        CORE_SUBJECTS.forEach(subject => {
          subjects.push(subject);
          existingSubjectIds.add(subject.id);
        });

        // 遍历 subjectScores 中的科目
        Object.entries(this.subjectScores).forEach(([subjectId, score]) => {
          // 如果不是必选科目，添加到结果数组
          if (!existingSubjectIds.has(subjectId)) {
            // 根据科目 ID 获取科目名称
            let subjectName = '';
            switch(subjectId) {
              case 'physics': subjectName = '物理'; break;
              case 'chemistry': subjectName = '化学'; break;
              case 'biology': subjectName = '生物'; break;
              case 'politics': subjectName = '政治'; break;
              case 'history': subjectName = '历史'; break;
              case 'geography': subjectName = '地理'; break;
              case 'tech': subjectName = '技术'; break;
              default: subjectName = subjectId;
            }

            subjects.push({
              id: subjectId,
              name: subjectName
            });
            existingSubjectIds.add(subjectId);
          }
        });

        // console.log('使用 subjectScores 生成的科目列表:', subjects);
        return subjects;
      }

      // 如果用户还没有选择科目，直接返回必选科目
      if (!this.fkList || this.fkList.length === 0) {
        // console.log('未找到用户选择的科目，只返回必选科目');
        return selectedSubjects;
      }

      // 找到科目选择题
      const subjectQuestion = this.questions.find(q => q.id === 3);
      if (!subjectQuestion) {
        // console.log('未找到科目选择题，只返回必选科目');
        return selectedSubjects;
      }

      // 常见科目与ID映射
      const subjectIdMap = {
        '语文': 'chinese',
        '数学': 'math',
        '英语': 'english',
        '物理': 'physics',
        '化学': 'chemistry',
        '生物': 'biology',
        '政治': 'politics',
        '历史': 'history',
        '地理': 'geography',
        '技术': 'tech',
        '心理学': 'psychology',
        '思想政治': 'politics',
        '体育': 'sports',
        '音乐': 'music',
        '美术': 'art',
        '选修': 'elective'
      };

      // 已存在的必选科目 ID
      const existingSubjectIds = new Set(selectedSubjects.map(s => s.id));

      // 逐个处理用户选择的附加科目 ID
      this.fkList.forEach(itemId => {
        // console.log('处理科目 ID:', itemId);

        // 如果是数字类型的ID或可转为数字的字符串
        const id = typeof itemId === 'number' ? itemId :
                 typeof itemId === 'string' && !isNaN(Number(itemId)) ? Number(itemId) : itemId;

        // 使用LabelFn方法获取科目名称
        const subjectName = this.LabelFn(id, subjectQuestion.questionChoiceDoS);
        // console.log('ID:', id, '获取到科目名称:', subjectName);

        if (subjectName) {
          // 使用预定义的科目代码，如果有的话
          const subjectCode = subjectIdMap[subjectName] || subjectName.toLowerCase().replace(/\s+/g, '_');

          // 如果不是已存在的必选科目，才添加到结果数组
          if (!existingSubjectIds.has(subjectCode)) {
            selectedSubjects.push({
              id: subjectCode,
              name: subjectName
            });
            // 记录已添加的ID，避免重复
            existingSubjectIds.add(subjectCode);
          }
        }
      });

      // console.log('最终科目列表(必选+选修):', selectedSubjects);
      return selectedSubjects;
    },
    LabelFn(val, arr) {
      let str = ''
      arr.forEach((item) => {
        if (item.id == val) {
          str = item.choiceContent
        }
      })
      return str
    },
    getUserInfo() {
      // 从本地存储获取用户ID
      const userId = uni.getStorageSync('userId');
      if (!userId) {
        console.error('未找到用户ID');
        return;
      }

      // console.log('正在获取用户信息，用户ID:', userId);

      // 调用API获取用户信息
      uni.request({
        url: this.$base.baseUrl + 'admin-api/system/user/get',
        method: 'GET',
        data: { id: userId },
        header: {
          'Authorization': uni.getStorageSync('token')
        },
        success: (res) => {
          // console.log('用户信息API响应:', res.data);

          if (res.data.code === 0 && res.data.data) {
            this.userInfo = res.data.data;
            // console.log('成功获取用户信息:', this.userInfo);

            // 设置省份（如果存在）
            if (this.userInfo.province) {
              // 查找匹配的省份选项
              const provinceQuestion = this.questions.find(q => q.id === 1);
              if (provinceQuestion && provinceQuestion.questionChoiceDoS) {
                const provinceChoice = provinceQuestion.questionChoiceDoS.find(
                  choice => choice.choiceContent === this.userInfo.province
                );

                if (provinceChoice) {
                  // 设置省份答案
                  this.$set(this.answers, 1, provinceChoice.id);
                  console.log('自动设置省份答案:', provinceChoice.id, '省份名称:', provinceChoice.choiceContent);
                  console.log('设置后的answers[1]:', this.answers[1]);
                  // 设置为已回答
                  this.isCurrentQuestionAnswered = true;
                  // 更新状态
                  this.checkAnswerStatus(false);
                } else {
                  console.log('未找到匹配的省份选项，用户省份:', this.userInfo.province);
                }
              }
            }

            // 设置总分（如果存在）
            if (this.userInfo.score !== undefined && this.userInfo.score !== null) {
              this.total = Number(this.userInfo.score);
              // console.log('从用户信息获取总分:', this.total);

              // 将总分设置为问题ID为5的答案
              this.$set(this.answers, 5, this.total.toString());

              // 确保总分已设置到界面上
              this.$nextTick(() => {
                // console.log('设置后的总分值:', this.total);
                // console.log('问题ID为5的答案:', this.answers[5]);
              });
            } else {
              // console.log('用户信息中没有总分字段或总分为空值');
              // 设置一个默认总分，便于测试（实际使用时可以移除）
              this.total = 0;
              // 将总分设置为问题ID为5的答案
              this.$set(this.answers, 5, '0');
            }

            // 从用户信息中获取各科分数信息
            if (this.userInfo.subjectScores) {
              try {
                // console.log('从用户信息加载各科分数:', this.userInfo.subjectScores);

                // 解析科目分数数据
                let subjectScoresData;
                if (typeof this.userInfo.subjectScores === 'string') {
                  // 可能是JSON字符串或逗号分隔的字符串
                  try {
                    subjectScoresData = JSON.parse(this.userInfo.subjectScores);
                  } catch (e) {
                    // 可能是逗号分隔的字符串
                    const scoreItems = this.userInfo.subjectScores.split(',');
                    subjectScoresData = {};

                    // 尝试解析类似 "语文:120" 这样的格式
                    scoreItems.forEach(item => {
                      const parts = item.split(':');
                      if (parts.length === 2) {
                        const subjectName = parts[0].trim();
                        const score = parts[1].trim();

                        // 映射科目名称到ID
                        let subjectId = '';
                        switch(subjectName) {
                          case '语文': subjectId = 'chinese'; break;
                          case '数学': subjectId = 'math'; break;
                          case '英语': subjectId = 'english'; break;
                          case '物理': subjectId = 'physics'; break;
                          case '化学': subjectId = 'chemistry'; break;
                          case '生物': subjectId = 'biology'; break;
                          case '政治': subjectId = 'politics'; break;
                          case '历史': subjectId = 'history'; break;
                          case '地理': subjectId = 'geography'; break;
                          case '技术': subjectId = 'tech'; break;
                          default: subjectId = subjectName.toLowerCase().replace(/\s+/g, '_');
                        }

                        subjectScoresData[subjectId] = score;
                      }
                    });
                  }
                } else if (typeof this.userInfo.subjectScores === 'object') {
                  // 已经是对象
                  subjectScoresData = this.userInfo.subjectScores;
                }

                // console.log('解析后的科目分数数据:', subjectScoresData);

                // 更新到组件的subjectScores对象
                if (subjectScoresData && typeof subjectScoresData === 'object') {
                  // 确保我们不会丢失原来的subjectScores结构
                  const updatedScores = { ...this.subjectScores };

                  // 更新每个科目的分数
                  Object.keys(subjectScoresData).forEach(subjectId => {
                    updatedScores[subjectId] = String(subjectScoresData[subjectId]);
                    // console.log(`设置科目 ${subjectId} 分数为:`, updatedScores[subjectId]);
                  });

                  // 更新组件的subjectScores
                  this.subjectScores = updatedScores;

                  // 更新科目分数题的答案
                  // 准备answers[6]数组
                  const scoreAnswers = [];

                  // 添加基础科目
                  if (updatedScores.chinese) {
                    scoreAnswers.push({ name: '语文', score: updatedScores.chinese });
                  }
                  if (updatedScores.math) {
                    scoreAnswers.push({ name: '数学', score: updatedScores.math });
                  }
                  if (updatedScores.english) {
                    scoreAnswers.push({ name: '英语', score: updatedScores.english });
                  }

                  // 添加其他科目
                  Object.keys(updatedScores).forEach(subjectId => {
                    if (subjectId !== 'chinese' && subjectId !== 'math' && subjectId !== 'english' && updatedScores[subjectId]) {
                      let subjectName = '';
                      switch(subjectId) {
                        case 'physics': subjectName = '物理'; break;
                        case 'chemistry': subjectName = '化学'; break;
                        case 'biology': subjectName = '生物'; break;
                        case 'politics': subjectName = '政治'; break;
                        case 'history': subjectName = '历史'; break;
                        case 'geography': subjectName = '地理'; break;
                        case 'tech': subjectName = '技术'; break;
                        default: subjectName = subjectId.replace(/_/g, ' ');
                      }

                      if (subjectName) {
                        scoreAnswers.push({ name: subjectName, score: updatedScores[subjectId] });
                      }
                    }
                  });

                  // 设置answers[6]
                  this.$set(this.answers, 6, scoreAnswers);
                  // console.log('设置科目分数答案:', scoreAnswers);

                  // 更新fsList
                  this.updateFsList();

                  // 强制更新视图
                  this.$forceUpdate();

                  // 如果当前是科目分数题，则更新题目状态
                  const scoreQuestionIndex = this.questions.findIndex(q => q.id === 6);
                  if (scoreQuestionIndex !== -1 && this.currentQuestion === scoreQuestionIndex) {
                    this.updateCurrentQuestionAnsweredStatus();
                    this.loadScoreData(); // 重新加载分数数据
                  }
                }
              } catch (error) {
                console.error('处理科目分数数据失败:', error);
              }
            } else {
              // console.log('用户信息中没有科目分数信息');
            }

            // 从用户信息中获取选科信息
            if (this.userInfo.secondSubject) {
              try {
                // 处理secondSubject字段，可能是逗号分隔的字符串
                let subjectsData;
                if (typeof this.userInfo.secondSubject === 'string') {
                  // 将逗号分隔的字符串转换为数组
                  subjectsData = this.userInfo.secondSubject.split(',').map(item => item.trim());
                } else if (Array.isArray(this.userInfo.secondSubject)) {
                  subjectsData = this.userInfo.secondSubject;
                } else {
                  subjectsData = [];
                }

                // console.log('成功从用户信息获取选科数据:', subjectsData);

                // 查找科目选择题
                const subjectQuestion = this.questions.find(q => q.id === 3);
                if (subjectQuestion && subjectQuestion.questionChoiceDoS) {
                  // console.log('选科题选项:', subjectQuestion.questionChoiceDoS);

                  // 对每个用户选科，找到对应的选项ID
                  const selectedIds = [];
                  subjectsData.forEach(subjectName => {
                    const choice = subjectQuestion.questionChoiceDoS.find(c =>
                      c.choiceContent === subjectName);
                    if (choice) {
                      selectedIds.push(choice.id);
                      // console.log(`找到选科 ${subjectName} 的ID: ${choice.id}`);
                    } else {
                      // console.log(`未找到选科 ${subjectName} 对应的选项`);
                    }
                  });

                  // 设置选科答案
                  this.$set(this.answers, 3, selectedIds);
                  this.fkList = selectedIds;
                  // console.log('设置选科答案:', selectedIds);

                  // 将选中的选项标记为已选中（设置checked属性）
                  // 先深拷贝选项数组，避免引用问题
                  const choicesCopy = JSON.parse(JSON.stringify(subjectQuestion.questionChoiceDoS));

                  // 先重置所有选项的选中状态
                  choicesCopy.forEach(choice => {
                    choice.checked = false;
                  });

                  // 然后将选中的选项标记为已选中
                  choicesCopy.forEach(choice => {
                    if (selectedIds.includes(choice.id)) {
                      choice.checked = true;
                      // console.log(`将选项 ${choice.choiceContent} 标记为已选中`);
                    }
                  });

                  // 使用Vue的响应式更新整个数组
                  this.$set(subjectQuestion, 'questionChoiceDoS', choicesCopy);

                  // 打印选中的选项，用于调试
                  // console.log('选中的选项：', subjectQuestion.questionChoiceDoS.filter(item => item.checked).map(item => item.choiceContent));

                  // 强制刷新视图
                  this.$forceUpdate();

                  // 延时执行，确保视图已经更新
                  setTimeout(() => {
                    // 再次强制刷新视图
                    this.$forceUpdate();
                    // console.log('延时后再次强制刷新视图');

                    // 再次打印选中的选项，用于调试
                    // console.log('延时后选中的选项：', subjectQuestion.questionChoiceDoS.filter(item => item.checked).map(item => item.choiceContent));
                  }, 100);

                  // 更新当前题目的答题状态
                  if (this.currentQuestion === 2) { // 如果当前题目就是选科题
                    this.updateCurrentQuestionAnsweredStatus();
                  }

                  // 生成选中科目列表用于分数题
                  const selectedSubjects = selectedIds.map(subjectId => {
                    const choice = subjectQuestion.questionChoiceDoS.find(c => c.id === subjectId);
                    const name = choice ? choice.choiceContent : '';
                    return {
                      name: name,
                      score: ''
                    };
                  });

                  // 基础科目
                  let basicSubjects = [{
                    name: '语文',
                    score: ''
                  }, {
                    name: '数学',
                    score: ''
                  }, {
                    name: '英语',
                    score: ''
                  }];

                  // 初始化subjectScores对象
                  this.subjectScores = {
                    'chinese': '',
                    'math': '',
                    'english': ''
                  };

                  // 将选中的科目添加到subjectScores中
                  selectedSubjects.forEach(subject => {
                    // 根据科目名称生成ID
                    let subjectId = '';
                    switch(subject.name) {
                      case '物理': subjectId = 'physics'; break;
                      case '化学': subjectId = 'chemistry'; break;
                      case '生物': subjectId = 'biology'; break;
                      case '政治': subjectId = 'politics'; break;
                      case '历史': subjectId = 'history'; break;
                      case '地理': subjectId = 'geography'; break;
                      case '技术': subjectId = 'tech'; break;
                      default: subjectId = subject.name.toLowerCase().replace(/\s+/g, '_');
                    }
                    this.subjectScores[subjectId] = '';
                  });

                  // 合并必修科目和选修科目用于分数题
                  const allSubjects = [...basicSubjects, ...selectedSubjects];

                  // 更新分数题的科目列表
                  const scoreQuestion = this.questions.find(q => q.id === 6);
                  if (scoreQuestion) {
                    scoreQuestion.subjects = allSubjects;
                  }
                }
              } catch (error) {
                console.error('处理选科数据失败:', error);
              }
            } else {
              // console.log('用户信息中没有选科信息');
            }

            // 解析推荐专业数据
            if (this.userInfo.recommendedMajors) {
              try {
                // 尝试解析JSON字符串
                let recommendedMajorsData;
                if (typeof this.userInfo.recommendedMajors === 'string') {
                  recommendedMajorsData = JSON.parse(this.userInfo.recommendedMajors);
                } else {
                  recommendedMajorsData = this.userInfo.recommendedMajors;
                }

                // console.log('成功解析推荐专业数据:', recommendedMajorsData);

                // 保存原始推荐专业数据
                this.recommendedMajorsData = recommendedMajorsData;

                // 直接从用户信息中加载专业数据，不再调用getSubject API
                this.processRecommendedMajors(recommendedMajorsData);
              } catch (error) {
                console.error('解析推荐专业数据失败:', error);
              }
            } else {
              // console.log('用户信息中没有推荐专业数据');
            }
          } else {
            console.error('获取用户信息失败:', res.data.msg || '未知错误');
          }
        },
        fail: (err) => {
          console.error('获取用户信息请求失败:', err);
        },
        complete: () => {
          // console.log('用户信息请求完成，当前总分:', this.total);
        }
      });
    },

    // 新的专业展示相关方法
    selectCategory(index) {
      // 防止重复点击
      if (this.isProcessingSelection) {
        return;
      }

      this.selectedCategoryIndex = index;
      this.selectedCategory = this.items[index];
      this.isCategoryExpanded = true; // 切换分类时自动展开
    },

    toggleCategoryExpand() {
      // 防止重复点击
      if (this.isProcessingSelection) {
        return;
      }

      this.isCategoryExpanded = !this.isCategoryExpanded;
    },

    getTotalMajorCount() {
      if (!this.selectedCategory || !this.selectedCategory.children) return 0;
      let count = 0;
      this.selectedCategory.children.forEach(subCategory => {
        if (subCategory.children) {
          count += subCategory.children.length;
        }
      });
      return count;
    },

    getAllMajorsInCategory() {
      if (!this.selectedCategory || !this.selectedCategory.children) return [];
      let majors = [];
      this.selectedCategory.children.forEach(subCategory => {
        if (subCategory.children) {
          majors = majors.concat(subCategory.children);
        }
      });
      return majors;
    },



    // 判断是否为专科专业
    isZhuanke(major) {
      return major.educationLevel === '高职（专科）';
    },

    // 获取专业修学年限
    getMajorDuration(major) {
      if (this.isZhuanke(major)) {
        return '三年';
      }
      // 检查是否为医学专业（通过专业名称或分类判断）
      if (this.isMedicalMajor(major)) {
        return '五年';
      }
      return major.duration || '四年';
    },

    // 判断是否为医学专业
    isMedicalMajor(major) {
      // 通过专业名称判断
      const medicalKeywords = ['医学', '临床', '口腔', '预防', '中医', '针灸', '推拿', '法医', '基础医学', '麻醉', '医学影像', '眼视光', '精神医学', '放射医学', '妇幼保健'];
      const majorName = major.name || '';

      // 检查专业名称是否包含医学相关关键词
      for (let keyword of medicalKeywords) {
        if (majorName.includes(keyword)) {
          return true;
        }
      }

      // 通过当前选中的分类判断（如果当前在医学分类下）
      if (this.selectedCategory && this.selectedCategory.name && this.selectedCategory.name.includes('医学')) {
        return true;
      }

      return false;
    },

    // 格式化男女比例显示
    formatMaleFemaleRatio(ratio) {
      if (!ratio) {
        return '暂无数据';
      }

      // 如果是字符串且包含冒号，说明是比例格式如"35:65"
      if (typeof ratio === 'string' && ratio.includes(':')) {
        const parts = ratio.split(':');
        if (parts.length === 2) {
          const maleRatio = parseFloat(parts[0]);
          const femaleRatio = parseFloat(parts[1]);
          if (!isNaN(maleRatio) && !isNaN(femaleRatio)) {
            // 转换为男性比例的百分比
            const total = maleRatio + femaleRatio;
            const malePercent = Math.round((maleRatio / total) * 100);
            return `${malePercent}%`;
          }
        }
        return ratio; // 如果解析失败，返回原始值
      }

      // 如果是数字，转换为百分比格式
      if (typeof ratio === 'number') {
        return `${ratio}%`;
      }

      // 如果是字符串，检查是否已经包含%
      if (typeof ratio === 'string') {
        if (ratio.includes('%')) {
          return ratio;
        } else {
          // 尝试解析为数字
          const numRatio = parseFloat(ratio);
          if (!isNaN(numRatio)) {
            return `${numRatio}%`;
          }
        }
      }

      return ratio;
    },

    // 获取男性比例百分比
    getMaleRatioPercent(ratio) {
      if (!ratio || ratio === null || ratio === 'null') return 50;

      if (typeof ratio === 'string' && ratio.includes(':')) {
        const parts = ratio.split(':');
        if (parts.length === 2) {
          const maleRatio = parseFloat(parts[0]);
          const femaleRatio = parseFloat(parts[1]);
          if (!isNaN(maleRatio) && !isNaN(femaleRatio) && maleRatio >= 0 && femaleRatio >= 0) {
            const total = maleRatio + femaleRatio;
            if (total > 0) {
              return Math.round((maleRatio / total) * 100);
            }
          }
        }
      }

      // 如果是数字格式
      if (typeof ratio === 'number' && !isNaN(ratio) && ratio >= 0 && ratio <= 100) {
        return Math.round(ratio);
      }

      return 50; // 默认50%
    },

    // 获取女性比例百分比
    getFemaleRatioPercent(ratio) {
      if (!ratio || ratio === null || ratio === 'null') return 50;

      if (typeof ratio === 'string' && ratio.includes(':')) {
        const parts = ratio.split(':');
        if (parts.length === 2) {
          const maleRatio = parseFloat(parts[0]);
          const femaleRatio = parseFloat(parts[1]);
          if (!isNaN(maleRatio) && !isNaN(femaleRatio) && maleRatio >= 0 && femaleRatio >= 0) {
            const total = maleRatio + femaleRatio;
            if (total > 0) {
              return Math.round((femaleRatio / total) * 100);
            }
          }
        }
      }

      // 如果是数字格式，女性比例 = 100 - 男性比例
      if (typeof ratio === 'number' && !isNaN(ratio) && ratio >= 0 && ratio <= 100) {
        return Math.round(100 - ratio);
      }

      return 50; // 默认50%
    },

    // 验证男女比例数据是否有效
    isValidMaleFemaleRatio(ratio) {
      if (!ratio || ratio === null || ratio === 'null' || ratio === undefined || ratio === 'undefined') {
        return false;
      }

      // 检查是否为有效的比例格式 "35:65"
      if (typeof ratio === 'string' && ratio.includes(':')) {
        const parts = ratio.split(':');
        if (parts.length === 2) {
          const maleRatio = parseFloat(parts[0]);
          const femaleRatio = parseFloat(parts[1]);
          return !isNaN(maleRatio) && !isNaN(femaleRatio) && maleRatio >= 0 && femaleRatio >= 0;
        }
      }

      // 检查是否为有效的数字格式
      if (typeof ratio === 'number') {
        return !isNaN(ratio) && ratio >= 0 && ratio <= 100;
      }

      return false;
    },

    // 获取专业学位
    getMajorDegree(major) {
      if (this.isZhuanke(major)) {
        return ''; // 专科不授予学位
      }
      return major.degree || '学士';
    },

    // 处理推荐专业数据
    processRecommendedMajors(recommendedMajorsData) {
      if (!recommendedMajorsData || !recommendedMajorsData.categories) {
        console.error('推荐专业数据格式不正确');
        return;
      }

      // 输出原始数据中的本科专业列表，用于调试
      // console.log('====================查看本科专业信息==================');
      let undergraduateCount = 0;
      let vocationalCount = 0;
      let collegeCount = 0;

      // 遍历每个专业类别
      recommendedMajorsData.categories.forEach(category => {
        if (category.children) {
          category.children.forEach(subCategory => {
            if (subCategory.children) {
              subCategory.children.forEach(major => {
                if (major.educationLevel) {
                  const level = this.normalizeEducationLevel(major.educationLevel);
                  if (level === '本科（普通教育）') {
                    undergraduateCount++;
                    if (undergraduateCount <= 5) {
                      // console.log(`本科普通教育专业: ${major.name}, ID: ${major.id}, 教育层次: ${major.educationLevel}`);
                    }
                  } else if (level === '本科（职业教育）') {
                    vocationalCount++;
                    if (vocationalCount <= 5) {
                      // console.log(`本科职业教育专业: ${major.name}, ID: ${major.id}, 教育层次: ${major.educationLevel}`);
                    }
                  } else if (level === '高职（专科）') {
                    collegeCount++;
                    if (collegeCount <= 5) {
                      // console.log(`高职专科专业: ${major.name}, ID: ${major.id}, 教育层次: ${major.educationLevel}`);
                    }
                  } else {
                    // console.log(`未识别教育层次: ${major.educationLevel} => ${level}`);
                  }
                }
              });
            }
          });
        }
      });

      // console.log(`专业总数统计 - 本科普通: ${undergraduateCount}, 本科职业: ${vocationalCount}, 高职专科: ${collegeCount}`);
      // console.log('====================查看本科专业信息结束==================');

      // 重置搜索相关状态
      this.searchKeyword = '';
      this.originalItems = null;

      // 判断是否需要按教育层次过滤
      // 如果当前是在意向专业题目且之前切换了教育层次，则需要按层次过滤
      if (this.currentQuestion >= 0 &&
          this.currentQuestion < this.questions.length &&
          this.questions[this.currentQuestion].id === 7) {
        this.filterByEducationLevel = true;
      }

      // 计算每个教育层次的专业数量
      this.calculateEducationLevelCounts(recommendedMajorsData);

      // 获取当前教育层次
      let currentLevel = this.currentEducationLevel;
      // console.log('当前教育层次:', currentLevel);

      // 如果当前教育层次没有专业，自动切换到有专业的教育层次
      if (this.educationLevelCounts[currentLevel] === 0) {
        // 尝试找到一个有专业的教育层次
        const levels = ['本科（普通教育）', '本科（职业教育）', '高职（专科）'];
        for (const level of levels) {
          if (this.educationLevelCounts[level] > 0) {
            console.log(`当前教育层次 ${currentLevel} 没有专业，自动切换到 ${level}`);
            this.currentEducationLevel = level;
            currentLevel = level;
            // 确保设置过滤标志
            this.filterByEducationLevel = true;
            break;
          }
        }
      }

      // 设置当前是否要按教育层次过滤
      this.filterByEducationLevel = true;

      // 确保当前教育层次是标准化的格式
      this.currentEducationLevel = this.normalizeEducationLevel(this.currentEducationLevel);

      // 处理专业数据
      let items = [];
      if (recommendedMajorsData.categories && recommendedMajorsData.categories.length > 0) {
    items = recommendedMajorsData.categories.map((item) => {
      let categoryItem = {
        id: item.id,
        name: item.name,
        text: item.name.substring(0, item.name.length - 2),
        badge: 'number',
        children: [],
        loaded: item.loaded || true
      };

      if (item.children && item.children.length > 0) {
        categoryItem.children = item.children.map((newitem) => {
          let subCategoryItem = {
            id: newitem.id,
            name: newitem.name,
            text: newitem.name,
            isshow: true,
            children: []
          };

          if (newitem.children && newitem.children.length > 0) {
            // 优化：避免深拷贝，直接处理原数据
            let filteredMajors = newitem.children;

            // 如果用户明确选择了教育层次，进行筛选
            if (this.filterByEducationLevel) {
              filteredMajors = newitem.children.filter(major => {
                // 如果专业没有educationLevel属性，默认显示
                if (!major.educationLevel) return true;

                // 标准化教育层次名称后比较
                return this.normalizeEducationLevel(major.educationLevel) === this.normalizeEducationLevel(currentLevel);
              });
            }

            subCategoryItem.children = filteredMajors.map((mynewitem) => {
              return {
                id: mynewitem.id,
                name: mynewitem.name,
                text: mynewitem.name,
                code: mynewitem.code,
                educationLevel: mynewitem.educationLevel,
                isRecommended: mynewitem.isRecommended || false,
                careerDirection: mynewitem.careerDirection || null,
                duration: mynewitem.duration || '四年',
                degree: mynewitem.degree || '学士',
                salary: mynewitem.salary || '￥10.3万',
                graduateScale: mynewitem.graduateScale || null,
                maleFemaleRatio: mynewitem.maleFemaleRatio || null
              };
            });
          }

          // 只返回有子项的子类别
          if (subCategoryItem.children && subCategoryItem.children.length > 0) {
            return subCategoryItem;
          }
          return null;
        }).filter(item => item !== null); // 过滤掉空项
      }

      // 只返回有子项的类别
      if (categoryItem.children && categoryItem.children.length > 0) {
        return categoryItem;
      }
      return null;
    }).filter(item => item !== null); // 过滤掉空项
  }

      // 更新专业数据
      this.items = items;

      // 初始化选中的分类
      if (items && items.length > 0) {
        this.selectedCategory = items[0];
        this.selectedCategoryIndex = 0;
      }

      // 调试输出：查看教育层次计数和推荐专业
      // console.log('教育层次专业数量:', JSON.stringify(this.educationLevelCounts));
      // console.log('hasAnyEducationLevel:', this.hasAnyEducationLevel);

      // 检查是否有推荐专业
      let recommendedCount = 0;
      items.forEach(category => {
        if (category.children) {
          category.children.forEach(subCategory => {
            if (subCategory.children) {
              subCategory.children.forEach(major => {
                if (major.isRecommended) {
                  recommendedCount++;
                  // console.log('找到推荐专业:', major.name, 'ID:', major.id);
                }
              });
            }
          });
        }
      });
      // console.log('推荐专业总数:', recommendedCount);

      // 如果是意向专业题，加载用户之前选择的专业
      if (this.currentQuestion >= 0 &&
          this.currentQuestion < this.questions.length &&
          this.questions[this.currentQuestion].id === 7) {

        // 如果已有答案，选中对应专业
        if (this.answers[7] && Array.isArray(this.answers[7])) {
          // console.log('找到之前选择的意向专业:', this.answers[7]);

          // 确保yxzy_ids初始化为答案数组的副本
          this.yxzy_ids = [...this.answers[7]];
          // console.log('设置当前选中专业列表:', this.yxzy_ids);
        }

        // 优化：延迟更新组件状态，避免阻塞UI
        this.$nextTick(() => {
          this.updateCurrentQuestionAnsweredStatus();

          // 如果使用了树形选择组件，更新组件状态
          if (this.$refs.treeSelect) {
            // 确保组件的active_ids与yxzy_ids同步
            this.$refs.treeSelect.active_ids = this.yxzy_ids;
            // 只调用必要的初始化方法
            if (this.$refs.treeSelect.initBadge) {
              this.$refs.treeSelect.initBadge();
            }
          }
        });
      }

      // 输出专业数据加载完成信息
      // console.log('从用户信息中加载专业数据完成，当前教育层次:', currentLevel, '，专业数量:', this.countMajors(items));
    },

    // 标准化教育层次名称，处理不同括号的情况和变体形式
    normalizeEducationLevel(level) {
      if (!level) return level;

      // 先统一括号格式：将英文括号替换为中文括号
      let normalized = level.replace(/\(/g, '（').replace(/\)/g, '）');

      // 对简单的教育层次进行标准化处理
      if (normalized === '本科') {
        normalized = '本科（普通教育）'; // 如果只有“本科”，默认转为“本科（普通教育）”
      } else if (normalized === '本科普通') {
        normalized = '本科（普通教育）';
      } else if (normalized === '本科职业' || normalized === '职业本科') {
        normalized = '本科（职业教育）';
      } else if (normalized === '高职' || normalized === '专科') {
        normalized = '高职（专科）';
      }

      // console.log(`教育层次标准化: ${level} => ${normalized}`);
      return normalized;
    },

    // 获取标准化的教育层次键名映射
    getEducationLevelMap() {
      const standardLevels = {
        '本科（普通教育）': 0,
        '本科（职业教育）': 0,
        '高职（专科）': 0
      };

      // 创建一个映射，包括不同括号格式的变体
      const levelMap = {};
      Object.keys(standardLevels).forEach(level => {
        const englishBracketVersion = level.replace(/（/g, '(').replace(/）/g, ')');
        levelMap[level] = level; // 标准版本映射到自身
        levelMap[englishBracketVersion] = level; // 英文括号版本映射到标准版本
      });

      return { standardLevels, levelMap };
    },

    // 计算每个教育层次的专业数量
    calculateEducationLevelCounts(recommendedMajorsData) {
      // 获取标准教育层次和映射
      const { standardLevels, levelMap } = this.getEducationLevelMap();

      // 重置计数
      this.educationLevelCounts = { ...standardLevels };

      // 如果没有数据，直接返回
      if (!recommendedMajorsData || !recommendedMajorsData.categories) return;

      // 遍历所有专业，按教育层次计数
      recommendedMajorsData.categories.forEach(category => {
        if (category.children) {
          category.children.forEach(subCategory => {
            if (subCategory.children) {
              subCategory.children.forEach(major => {
                if (major.educationLevel) {
                  // 标准化教育层次名称
                  const normalizedLevel = this.normalizeEducationLevel(major.educationLevel);

                  // 找到映射的标准教育层次名称
                  const mappedLevel = normalizedLevel;

                  // 如果有映射的教育层次，增加对应计数
                  if (this.educationLevelCounts[mappedLevel] !== undefined) {
                    this.educationLevelCounts[mappedLevel]++;
                  }
                } else {
                  // 如果没有教育层次属性，默认算作本科（普通教育）
                  this.educationLevelCounts['本科（普通教育）']++;
                }
              });
            }
          });
        }
      });

      // console.log('教育层次专业数量统计:', this.educationLevelCounts);
    },

    // 计算专业总数
    countMajors(items) {
      let count = 0;
      if (!items) return count;

      items.forEach(category => {
        if (category.children) {
          category.children.forEach(subCategory => {
            if (subCategory.children) {
              count += subCategory.children.length;
            }
          });
        }
      });

      return count;
    },

    handleMultiSelect(currentItem, id, myItems) {
      let questionId = id;

      // 如果是选科题(ID为3)，不允许修改
      if (questionId === 3) {
        uni.showToast({
          title: "选科信息来自用户资料，不可修改",
          icon: 'none'
        });
        return;
      }

      let arrs = []
      currentItem.checked = !currentItem.checked
      myItems.map((myitem) => {
        if (myitem.checked) {
          arrs.push(myitem.id)
        }
      })
      if (arrs.length > 3) {
        uni.showToast({
          title: "学生选课最多选择三门,请重新选择",
          icon: 'none'
        })
        // 如果超过三门，取消当前选择
        currentItem.checked = false;
        // 重新计算选中的科目
        arrs = [];
        myItems.map((myitem) => {
          if (myitem.checked) {
            arrs.push(myitem.id);
          }
        });
      }

      // 当第三题科目选择改变时，更新第四题对应的科目成绩
      if (questionId === 3) {
        // console.log('当前选择的科目:', arrs);
        // console.log('当前题目:', this.questions[this.currentQuestion]);

        // 更新fkList数组
        this.fkList = arrs;
        // 清空第四题的成绩，但保持总分不变
        this.$set(this.answers, 6, []);
        this.fsList = [];
        // 保持总分不变，不再重置为0
        // 确保总分如果还不存在，尝试获取
        if (!this.total && this.userInfo && this.userInfo.score) {
          this.total = Number(this.userInfo.score);
        }

        // 生成选中科目列表
        const subjects = arrs.map(subject => {
          const name = this.LabelFn(subject, this.questions[this.currentQuestion].questionChoiceDoS);
          // console.log('添加科目:', name);
          return {
            name: name,
            score: ''
          };
        });

        let arr = [{
          name: '语文',
          score: ''
        },
          {
            name: '数学',
            score: ''
          },
          {
            name: '外语',
            score: ''
          }
        ];
        let myname = ''
        this.questions[this.currentQuestion - 1].questionChoiceDoS.map((items) => {
          if (items.id == this.answers[this.questions[this.currentQuestion - 1].id]) {
            myname = items.choiceContent
          }
        })
        let myarr = arr.concat([{
          name: myname,
          score: ''
        }]).concat(subjects);

        // 记录科目数组
        this.subjects = myarr;
        // console.log('生成的科目数组:', myarr);

        // 确保第6题的subjects已设置
        this.$set(this.questions[6], 'subjects', myarr);

        // 初始化第6题的答案数组
        const initialAnswers = myarr.map(subject => ({
          name: subject.name,
          score: ''
        }));
        this.$set(this.answers, 6, initialAnswers);

        // 修改为Vue的响应式数据更新方式
        this.$set(this.answers, questionId, arrs);
      } else if (questionId === 7) {
        this.yxzyList = arrs;
        // 修改为Vue的响应式数据更新方式
        this.$set(this.answers, questionId, arrs);
      } else {
        // 修改为Vue的响应式数据更新方式
        this.$set(this.answers, questionId, arrs);
      }

      // 如果是选择科目题，不触发自动跳转
      if (questionId === 3) {
        // 只更新状态，不跳转
        // 选科题特殊处理 - 从用户信息中获取
        if (this.userInfo && this.userInfo.secondSubject) {
          // 如果用户信息中有选科数据，则认为已回答
          this.isCurrentQuestionAnswered = true;
          // console.log('选科题从用户信息中获取，设置为已回答');
        } else if (this.fkList && this.fkList.length > 0) {
          // 如果fkList中有数据，则认为已回答
          this.isCurrentQuestionAnswered = true;
          // console.log('从fkList中检测到选科数据');
        } else {
          this.isCurrentQuestionAnswered = false;
        }
        // console.log('科目选择已更新，答题状态:', this.isCurrentQuestionAnswered);
      } else {
        // 其他题目正常检查状态
        this.checkAnswerStatus(false); // 传入false表示不提交答案
      }
    },

    // 切换到上一题
    prevQuestion() {
      // console.log('触发prevQuestion，当前题目索引:', this.currentQuestion);

      // 防止逻辑重入
      if (this._isNavigating) {
        uni.showToast({
          title: '正在切换题目，请勿重复点击',
          icon: 'none'
        });
        return;
      }
      this._isNavigating = true;

      if (this.currentQuestion > 0) {
        // 找到ID为8、11的题目索引
        const question8Index = this.questions.findIndex(q => q.id === 8);
        const question11Index = this.questions.findIndex(q => q.id === 11);

        // 如果当前是ID为11的题目，则直接返回到ID为8的题目
        if (this.currentQuestion === question11Index && question8Index !== -1) {
          this.currentQuestion = question8Index; // 直接返回到ID为8的题目
        }
        else {
          this.currentQuestion = this.currentQuestion - 1;
        }

        uni.pageScrollTo({
          scrollTop: 0,
          duration: 100
        })

        // 如果回到分数题，重新检查分数总和
        if (this.questions[this.currentQuestion] && this.questions[this.currentQuestion].id === 6) {
          // console.log('回到分数题，检查分数');
          this.validateScoreSum();
        }

        // 返回到上一题时，检查该题是否已经回答
        this.$nextTick(() => {
          this.updateCurrentQuestionAnsweredStatus();
        });
      }

      // 重置导航标志，延迟是为了防止多次连击导致问题
      setTimeout(() => {
        this._isNavigating = false;
      }, 300);
    },

    nextQuestion() {
      // console.log(`nextQuestion 调用: 当前题目索引=${this.currentQuestion}, 问题总数=${this.questions ? this.questions.length : 0}`);

      // 防止逻辑重入
      if (this._isNavigating) {
        uni.showToast({
          title: '正在切换题目，请勿重复点击',
          icon: 'none'
        });
        return;
      }
      this._isNavigating = true;

      // 提交当前题目的答案
      // 注意：答案已在上面提交，这里不需要重复提交

      // 确保问题数组已加载
      if (this.questions && this.questions.length > 0 && this.currentQuestion < this.questions.length - 1) {
        // 如果当前是高考总分题（ID为5），直接允许进入下一题，因为总分从用户信息中获取，不需要验证
        if (this.questions[this.currentQuestion] && this.questions[this.currentQuestion].id === 5) {
          // 直接提交当前题目数据到服务器
          this.submitCurrentQuestionData();
          return;
        }

        // 检查当前是否是分数题，如果是则进行多项验证
        if (this.questions[this.currentQuestion] && this.questions[this.currentQuestion].id === 6) {
          // 1. 验证是否选择了科目
          const selectedSubjects = this.getSelectedSubjects();
          if (selectedSubjects.length === 0) {
            uni.showToast({
              title: '请先返回上一题选择科目，然后再输入分数',
              icon: 'none',
              duration: 2000
            });
            this._isNavigating = false;
            return;
          }

          // 2. 验证是否所有选择的科目都输入了分数
          const hasEmptyScores = selectedSubjects.some(subject => {
            const score = this.subjectScores[subject.id];
            return !score || score.toString().trim() === '' || isNaN(parseFloat(score));
          });

          if (hasEmptyScores) {
            uni.showToast({
              title: '请输入所有选择科目的分数',
              icon: 'none',
              duration: 2000
            });
            this._isNavigating = false;
            return;
          }

          // 3. 验证分数总和
          const currentSum = this.getCurrentSum();
          const difference = Math.abs(currentSum - this.total);

          // 如果分数总和与目标总分相差超过0.1，不允许前进
          if (difference > 0.1) {
            uni.showToast({
              title: `分数总和应为 ${this.total} 分，请检查各科分数`,
              icon: 'none',
              duration: 2000
            });
            this._isNavigating = false;
            return;
          }
        }

        // 提交当前题目数据到服务器
        this.submitCurrentQuestionData();
      } else {
        // console.log('已经是最后一题或问题数组未加载');
        this._isNavigating = false; // 重置导航标志
      }
    },

    // 提交特定题目的答案到服务器
    submitAnswerToServer(questionId, answer) {
      // 查找题目对象
      const question = this.questions.find(q => q.id === questionId);
      if (!question) {
        // console.log(`无法找到ID为${questionId}的题目`);
        return;
      }

      // 准备提交的答案数组
      let arrs = [];

      // 根据题目类型收集答案
      if (question.type === 1) { // 单选题
        if (answer !== undefined) {
          arrs.push(answer);
        }
      } else if (question.type === 2) { // 多选题
        if (questionId === 7) { // 第7题，专业选择
          arrs = Array.isArray(this.yxzy_ids) ? this.yxzy_ids : [];
        } else {
          arrs = Array.isArray(answer) ? answer : [];
        }
      } else { // 填空题或其他类型
        if (questionId === 6) {
          arrs = this.fsList;
        } else {
          arrs = [];
        }
      }

      // 获取答案编号
      let answerNoValue = this.answerNo && this.answerNo != '-1' ? this.answerNo : '';

      // 如果answerNo仍然是-1或者无效值，传递空字符串
      if (answerNoValue === -1 || answerNoValue === '-1') {
        answerNoValue = '';
      }

      // 提交答案到服务器
      this.$apis.commitQuestion({
        type: this.ver,
        questionId: questionId,
        writeContent: question.type === 3 ? (questionId === 6 ? '' : answer) : '',
        answerChoices: questionId !== 6 ? arrs : [],
        answerNo: answerNoValue
      }).then((res) => {
        if (res.code === 0) {
          // 保存返回的答案编号
          if (typeof res.data === 'object' && res.data !== null) {
            this.answerNo = res.data.answerNo;
            this.workId = res.data.recordId;
          } else {
            this.answerNo = res.data;
          }
        }
      });
    },

    // 提交当前题目数据到服务器
    submitCurrentQuestionData() {
      // 确保当前题目存在
      if (!this.questions || this.currentQuestion < 0 || this.currentQuestion >= this.questions.length) {
        // console.log('当前题目不存在或超出范围');
        this._isNavigating = false;
        return;
      }

      const currentQ = this.questions[this.currentQuestion];
      if (!currentQ) {
        // console.log('无法获取当前题目对象');
        this._isNavigating = false;
        return;
      }

      // console.log('准备提交题目数据，ID:', currentQ.id);

      // 调用checkAnswerStatus并传入true，表示需要提交答案到服务器
      this.checkAnswerStatus(true);

      // 检查是否是最后一题
      const isLastQuestion = this.currentQuestion === this.questions.length - 1;

      if (isLastQuestion) {
        // console.log('已完成最后一题，显示报告生成提示');
        // 显示页面中已定义的报告生成中的弹窗
        this.show = true; // 显示现有的u-popup组件
        // 重置导航标志
        this._isNavigating = false;
      } else {
        // 处理导航逻辑
        // 找到ID为8的题目索引（显示为序号6的题目）
        const question8Index = this.questions.findIndex(q => q.id === 8);

        // 如果当前题目是ID为8的题目，则跳过ID为9和10的题目，直接到ID为11的题目
        if (this.currentQuestion === question8Index) {
          // console.log('检测到当前是ID为8的题目，将跳过ID为9和10的题目');

          // 找到ID为11的题目索引
          const question11Index = this.questions.findIndex(q => q.id === 11);

          if (question11Index !== -1) {
            // 直接跳到ID为11的题目
            this.currentQuestion = question11Index;
            // console.log(`跳过ID为9和10的题目，直接到索引=${this.currentQuestion}`);

            // 提交ID为9和10的题目的答案（如果已经选择）
            if (this.answers[9]) {
              this.submitAnswerToServer(9, this.answers[9]);
            }
            if (this.answers[10]) {
              this.submitAnswerToServer(10, this.answers[10]);
            }

            // 切换到下一题后，检查该题是否已经回答
            this.$nextTick(() => {
              this.updateCurrentQuestionAnsweredStatus();
            });
          } else {
            // 如果找不到ID为11的题目，则正常前进
            this.currentQuestion = this.currentQuestion + 1;
            // console.log(`正常前进到索引=${this.currentQuestion}`);

            // 切换到下一题后，检查该题是否已经回答
            this.$nextTick(() => {
              this.updateCurrentQuestionAnsweredStatus();
            });
          }
        } else {
          // 正常前进到下一题
          this.currentQuestion = this.currentQuestion + 1;
          // console.log(`正常前进到索引=${this.currentQuestion}`);

          // 切换到下一题后，检查该题是否已经回答
          this.$nextTick(() => {
            this.updateCurrentQuestionAnsweredStatus();
          });
        }

        // 滚动到页面顶部
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 100
        });

        // 重置导航标志
        setTimeout(() => {
          this._isNavigating = false;
        }, 300);
      }
    },

    // 更新当前题目的答题状态
    updateCurrentQuestionAnsweredStatus() {
      // 获取当前题目的ID和答案
      const currentQ = this.questions[this.currentQuestion];
      if (currentQ) {
        const questionId = currentQ.id;
        const answer = this.answers[questionId];

        // console.log('检查题目是否已回答，ID:', questionId, '当前答案:', JSON.stringify(answer));

        // 判断该题是否已经回答
        let isAnswered = false;

        // 根据题目类型判断是否已经回答
        if (currentQ.type === 1) { // 单选题
          isAnswered = !!answer;
        } else if (currentQ.type === 2) { // 多选题
          if (questionId === 3) { // 选科题
            // 检查是否有选科数据（从用户信息中获取）
            if (Array.isArray(answer) && answer.length > 0) {
              isAnswered = true;
              // console.log('选科题已有答案:', answer);
            } else if (Array.isArray(this.fkList) && this.fkList.length > 0) {
              isAnswered = true;
              // console.log('从fkList中检测到选科:', this.fkList);
            } else {
              // 从用户信息中检查是否有选科数据
              if (this.userInfo && this.userInfo.secondSubject) {
                isAnswered = true;
                // console.log('从用户信息中检测到选科数据');
              } else {
                isAnswered = false;
              }
            }
          } else if (questionId === 7) { // 专业选择题
            isAnswered = Array.isArray(this.yxzy_ids) && this.yxzy_ids.length > 0;
          } else {
            isAnswered = Array.isArray(answer) && answer.length > 0;
          }
        } else if (currentQ.type === 3) { // 填空题
          if (questionId === 13) { // 第13题不必填
            isAnswered = true;
          } else if (questionId === 6) { // 分数题
            // 检查是否有分数数据
            if (this.subjectScores && Object.keys(this.subjectScores).length > 0) {
              // 检查是否有任何科目有分数
              const hasScores = Object.values(this.subjectScores).some(score => score && score.trim() !== '');
              if (hasScores) {
                // console.log('检测到科目分数数据:', this.subjectScores);
                isAnswered = true;
              } else {
                // 检查answers[6]数组是否有分数
                isAnswered = Array.isArray(answer) && answer.some(item => item && item.score);
              }
            } else if (Array.isArray(answer) && answer.length > 0) {
              // 如果没有subjectScores但有answers[6]数据，也认为是已回答
              isAnswered = answer.some(item => item && item.score);
              // console.log('从answers[6]中检测到分数数据:', answer);
            } else {
              isAnswered = false;
            }
          } else {
            isAnswered = !!answer;
          }
        }

        // 设置答题状态
        this.isCurrentQuestionAnswered = isAnswered;
        // console.log('更新当前题目答题状态为:', isAnswered);

        // 如果是选科题（ID为3），特殊处理
        if (questionId === 3 && this.userInfo && this.userInfo.secondSubject) {
          // 选科题从用户信息中获取，始终设置为已回答
          this.isCurrentQuestionAnswered = true;
          // console.log('选科题从用户信息中获取，设置为已回答');
        }

        // 强制刷新视图
        this.$forceUpdate();
      }
    },

    // 验证分数总和是否正确
    validateScoreSum() {
      if (!Array.isArray(this.answers[6]) || this.answers[6].length === 0) {
        // console.log('分数数组为空，无法验证');
        this.scoreSumError = true;
        return;
      }

      // 计算当前所有科目分数的总和
      const currentSum = this.answers[6].reduce((sum, subject) => {
        // 确保分数是数字，如果不是则转换或使用零
        const score = subject && subject.score !== undefined ? Number(subject.score) || 0 : 0;
        return sum + score;
      }, 0);

      // console.log(`分数总和: ${currentSum}, 目标总分: ${this.total}`);

      // 检查总分是否与预期的相等
      // 由于浮点数精度问题，使用差值比较
      if (Math.abs(currentSum - this.total) > 0.01) {
        this.scoreSumError = true;
        // 移除提示，不再显示分数总和提示
      } else {
        this.scoreSumError = false;
      }
    }
  }
}
</script>
<style>
/* 自定义单选按钮样式 */
radio {
  transform: scale(0.8);
}

radio .wx-radio-input {
  border-color: #fb6b3d !important;
}

radio .wx-radio-input.wx-radio-input-checked {
  background-color: #fb6b3d !important;
}

/* 自定义多选按钮样式 */
checkbox {
  transform: scale(0.8);
}

checkbox .wx-checkbox-input {
  border-color: #fb6b3d !important;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: #fb6b3d !important;
}

/* 修改选中对号颜色为白色 */
checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
  color: #fff !important;
  font-size: 32rpx;
}

checkbox .wx-checkbox-input {
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  background: #ffffff !important;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  border-color: #fb6d3f !important;
  background: #ffffff !important;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  font-size: 28rpx;
  color: #fff;
  background: #fb6d3f !important;
  transform: translate(-50%, -50%) scale(1);
  -webkit-transform: translate(-50%, -50%) scale(1);
}

page {
  background: #fef1db
}
</style>
<style lang="scss" scoped>
.radio-list {
  display: flex;
  flex-direction: column;
  padding: 10rpx 0;

  .item {
    padding: 10rpx 0;
    display: flex;
    align-items: center;

    .icon {
      margin-right: 20rpx;
      display: flex;
      align-items: center;
      padding-top: 6rpx;

      image {
        width: 48rpx;
        height: 48rpx;
      }
    }

    .text {
      flex: 1;
      font-size: 32rpx;
      color: #333;
    }

    &.cur {
      .text {
        color: #fb6d40;
      }
    }
  }
}

.test {
  .title {
    padding: 20rpx;
    font-weight: 700;
  }

  .val {
    display: flex;
    align-items: center;
    height: 80rpx;
    padding: 0 20rpx;
    background: #ffffff;
    margin-bottom: 20rpx;
  }
}

.dx-part {
  padding: 20rpx;
  display: flex;
  position: relative;
  flex-direction: row;

  .dx-left {
    width: 160rpx;
    background: #f7f7f7;
    display: flex;
    flex-direction: column;
    padding: 0rpx 0;
    border-radius: 20rpx 0 0 20rpx;

    .item {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-bottom: 5rpx;
      justify-content: center;
      font-size: 26rpx;
      color: #999;
      position: relative;
      padding: 22rpx 0;

      .zy-list {
        position: absolute;
        left: 200rpx;
        background: #ff0000;
        display: none;
        top: 10rpx;
        display: flex;
        flex-direction: column;

        .myitem {
          font-size: 28rpx;
          padding: 20rpx 0;
          color: #333;

          &.active {
            color: #fb6b3d;
          }
        }
      }

      &.active {
        background: #fff0eb;
        color: #fb6b3d;
        border-radius: 20rpx 0 0 20rpx;

        .zy-list {
          display: block;
        }
      }
    }
  }

  .dx-right {
    padding-top: 10rpx;
    margin-left: 20rpx;
  }
}

.closeicon {
  position: absolute;
  right: -25rpx;
  top: 5rpx;

  image {
    width: 64rpx;
    height: 64rpx;
  }
}

.picbox {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 600rpx;
}

.myboxs {
  margin: 100rpx 30rpx 20rpx 30rpx;
  height: 450rpx;
  flex-direction: column;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;

  .tit2 {
    margin-bottom: 40rpx;
    color: #AA7248;
    margin-top: 30rpx;
    font-size: 42rpx;
    font-weight: 700;
  }

  .cont {
    margin-bottom: 20rpx;

    text {
      font-weight: 700;
      letter-spacing: 0rpx;
      font-size: 26rpx;
      color: #AA7248;
      line-height: 42rpx;

      .bold {
        color: #e6702b;
        margin: 0 4rpx;
      }
    }
  }

  .jiazaizhong {
    margin-bottom: 20rpx;
  }

  .btn {
    button {
      color: #fff;
      padding: 5rpx 80rpx;
      border-radius: 70rpx;
      font-size: 32rpx;
      background-image: linear-gradient(90deg, #FF8C38 0, #FD5819 100%);

      &::after {
        border: none;
      }
    }
  }
}

.major-container {
  display: flex;
  margin-top: 20rpx;
}

.major-categories {
  width: 200rpx;
  border-right: 1rpx solid #eee;
}

.category-item {
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;

  &.active {
    color: #FB6B3D;
    background-color: #FFF0EB;
  }
}

.major-subcategories {
  flex: 1;
  padding-left: 20rpx;
}

.subcategory-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;

  text {
    margin-left: 20rpx;
    font-size: 28rpx;
    color: #333;
  }
}

.sexList {
  padding: 35rpx 0;
  margin: 0 -40rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .myitem {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;

    .img {
      position: relative;

      image {
        width: 270rpx;
        height: 270rpx;
      }
    }

    .text {
      margin-top: 15rpx;
      font-size: 32rpx;
      font-weight: 700;
      color: #333;
    }

    &.active {
      .text {
        color: #FB6B3D;
      }
    }
  }
}

.cityList {
  margin: 0 -20rpx;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  .myitem {
    width: calc(25% - 24rpx);
    margin: 0 10rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    background: #f6f6f6;
    border: 2rpx solid #f6f6f6;
    height: 64rpx;
    line-height: 64rpx;
    font-size: 26rpx;
    color: #333;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border-radius: 8rpx;

    &.active {
      border-color: #FB6B3D;
      background: #FFF0EB;
      color: #FB6B3D
    }
  }
}

.input-field {
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  margin-top: 20rpx;
  line-height: 80rpx;
  background-color: #fff;
}

.bg {
  // min-height: 100vh;
  // background-image: linear-gradient(180deg, #fc9a6f 0, #fff6e5 100%);

}

.container {
  padding: 10rpx 20rpx;
  position: relative;
  padding-bottom: 120rpx;
  box-sizing: border-box;
  flex-direction: row;
  align-items: center;
  margin-top: 10rpx;
}

.question-container {
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  overflow: hidden;
  position: relative;
  padding-top: 60rpx;
}

.question-header {
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1;
  height: 60rpx;
  line-height: 60rpx;
}

.question-number {
  font-size: 32rpx;
  color: #fff;
  font-weight: 700;
  min-width: 40rpx;
  text-align: center;
  padding: 6rpx 20rpx 6rpx 15rpx;
  border-radius: 0 40rpx 40rpx 0;
  margin-right: 15rpx;
  background: #fb6d3f;
}

.question-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 700;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 20rpx;
}

.question-content {
  padding: 0 30rpx;
  padding-bottom: 40rpx;
  display: flex;
  flex-direction: column;
  flex: 1;

  .tree-select-container {
    margin: 0 -40rpx; /* 扩展超出父元素的内边距 */
    height: 85vh; /* 增加高度以显示更多内容 */
    overflow: hidden;
    position: relative;
    background-color: #fff;

    /* 小字体树形选择器的样式 */
    .small-font-tree-select {
      .select_l {
        .item {
          .txt {
            padding: 22rpx 0;

            .text {
              font-size: 22rpx;
            }
          }
        }
      }
    }

    /* 搜索框样式 */
    .search-box {
      padding: 20rpx 30rpx;
      background-color: #fff;

      .search-input {
        position: relative;
        height: 70rpx;
        background-color: #f5f5f5;
        border-radius: 35rpx;
        display: flex;
        align-items: center;
        padding: 0 20rpx;

        .search-icon {
          width: 40rpx;
          height: 40rpx;
          margin-right: 10rpx;
        }

        input {
          flex: 1;
          height: 70rpx;
          font-size: 28rpx;
        }

        .placeholder {
          color: #999;
        }

        .clear-icon {
          width: 40rpx;
          height: 40rpx;
          line-height: 40rpx;
          text-align: center;
          font-size: 32rpx;
          color: #999;
          font-weight: bold;
        }
      }
    }

    /* 搜索模式提示样式 */
    .search-mode-tip {
      background-color: #FFF0EB;
      padding: 20rpx 0;
      margin: 0 30rpx 20rpx 30rpx;
      border-radius: 12rpx;
      box-shadow: 0 4rpx 12rpx rgba(251, 107, 61, 0.1);

      .tip-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 30rpx;
        font-size: 28rpx;
        color: #FB6B3D;

        .clear-search {
          color: #FB6B3D;
          font-weight: bold;
          padding: 10rpx 20rpx;
          border: 1px solid #FB6B3D;
          border-radius: 30rpx;
        }
      }
    }

    /* 标签页样式 */
    .tab-bar {
      display: flex;
      background-color: #fff;
      border-bottom: 1rpx solid #f0f0f0;

      .tab-item {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        font-size: 28rpx;
        color: #333;
        position: relative;

        &.active {
          color: #FB6B3D;
          font-weight: bold;

          &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60%;
            height: 6rpx;
            background-color: #FB6B3D;
            border-radius: 3rpx;
          }
        }

        &.no-data {
          color: #999;
          font-size: 26rpx;
        }
      }
    }

    /* 热门标签样式 */
    .hot-tags {
      display: flex;
      justify-content: space-between;
      padding: 20rpx 30rpx;
      background-color: #f8f8f8;

      .hot-tag-title {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;

        &.right {
          color: #666;
          font-weight: normal;
        }
      }
    }

    .tree_select {
      height: 600rpx !important;

      .select_l {
        width: 220rpx; /* 增加左侧区域宽度，确保能显示长名称 */
        flex: none;
        background-color: #f8f8f8; /* 浅灰色背景 */

        .scroll_view {
          height: 100% !important;
        }

        .item {
          padding: 0;
          border-bottom: 1rpx solid #eee;

          .txt {
            padding: 22rpx 0;
            display: flex;
            justify-content: center;

            .text {
              font-size: 22rpx;
              white-space: normal; /* 允许换行 */
              overflow: visible; /* 不隐藏溢出 */
              text-overflow: unset;
              max-width: none; /* 不限制最大宽度 */
              text-align: left; /* 左对齐更自然 */
              color: #666;
              word-break: break-all; /* 长单词换行 */
            }
          }

          &.on {
            background-color: #FFF0EB; /* 选中状态背景色 */

            .txt .text {
              color: #FB6B3D; /* 选中状态文字颜色 */
            }
          }
        }
      }

      .select_r {
        flex: 1;
        background-color: #fff;

        .scroll_view {
          height: 100% !important;
          padding-bottom: 20rpx;
        }

        .myitem {
          margin-bottom: 20rpx;

          .tit2 {
            padding: 20rpx 30rpx;

            .left {
              font-size: 32rpx;
              font-weight: bold;
              color: #333;
            }
          }

          .cont2 {
            margin-top: 10rpx;
          }
        }

        /* 专业详情样式 */
        .major-detail {
          padding: 20rpx 0;
          border-bottom: 1rpx solid #f5f5f5;
          position: relative;

          &.active {
            background-color: #FB6B3D; /* 使用纯色橙色背景 */
            border-left: 12rpx solid #E84D00; /* 更宽的左侧边框，颜色更深 */
            box-shadow: 0 4rpx 12rpx rgba(251, 107, 61, 0.4); /* 增强阴影效果 */
            transform: translateX(4rpx); /* 微小位移效果 */
            transition: all 0.2s ease; /* 添加过渡效果 */

            .major-name {
              color: #FFFFFF; /* 白色文字，与橙色背景形成强烈对比 */
              font-weight: bold; /* 加粗文字 */
              padding-left: 10rpx; /* 增加左侧内边距 */
            }

            .major-info {
              .info-label, .info-value {
                color: #FFFFFF; /* 信息文字也改为白色 */
              }
            }
          }

          .major-name {
            padding: 0 30rpx;
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 15rpx;
            position: relative;

            .selected-icon {
              position: absolute;
              right: 30rpx;
              top: 0;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .major-info {
            display: flex;
            flex-wrap: wrap;
            padding: 0 30rpx;

            .info-row {
              width: 50%;
              display: flex;
              padding: 8rpx 0;

              .info-label {
                width: 140rpx;
                font-size: 26rpx;
                color: #999;
              }

              .info-value {
                font-size: 26rpx;
                color: #333;
              }
            }
          }
        }

        .myitem2 {
          width: 100%;
          padding: 20rpx 30rpx;
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          border-bottom: 1rpx solid #f5f5f5;

          &.active {
            color: #FB6B3D;
          }
        }
      }
    }
  }
}

.option-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.option-item:last-child {
  border-bottom: none;
}

.option-item text {
  margin-left: 20rpx;
  font-size: 30rpx;
  color: #333;
}


.input-field {
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  margin-top: 20rpx;
}

.subjects-container {
  display: flex;
  margin: 20rpx -20rpx;
  flex-direction: row;
  flex-wrap: wrap;
}

.subject-item {
  width: 50%;
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
  flex-direction: row;

  .unit {
    font-size: 24rpx;
  }
}

.subject-item:last-child {
  border-bottom: none;
}

.subject-name {
  font-size: 28rpx;
  color: #333;
}

.subject-selection-info {
  width: 100%;
  padding: 40rpx 30rpx;
  background-color: #FFF0EB;
  border-radius: 10rpx;
  margin: 30rpx 0;
  box-sizing: border-box;
  text-align: center;
}

.subject-selection-info {
  width: 100%;
  padding: 30rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.selected-subjects-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.selected-subjects-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
  width: 100%;
  min-height: 80rpx;
}

.subject-selection-note {
  font-size: 28rpx;
  color: #FB6B3D;
  font-weight: bold;
  line-height: 1.5;
}

.subject-choice-item {
  display: inline-flex;
  align-items: center;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
}

.subject-choice-item.selected {
  color: #FB6B3D;
  font-weight: bold;
  background-color: #FFF0EB;
  border: 1px solid #FB6B3D;
}

.no-subjects {
  color: #FB6B3D;
  font-size: 28rpx;
  padding: 20rpx;
  text-align: center;
  width: 100%;
  background-color: #FFF0EB;
  border-radius: 8rpx;
}

.subject-item.selected {
  color: #FB6B3D;
  font-weight: bold;
  margin-right: 20rpx;
  padding: 10rpx 20rpx;
  background-color: #FFF0EB;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
}

.subject-item.not-selected {
  color: #999;
  margin-right: 20rpx;
  padding: 10rpx 20rpx;
}



.subject-score {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  margin-left: 20rpx;
}

.button-container {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 20rpx;
  box-sizing: border-box;
  background-color: #fff;
  position: fixed;
  padding-bottom: 20rpx;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  bottom: 0;
  left: 0;
  right: 0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
  border-top: 1rpx solid #f0f0f0;
}

.nav-button {
  flex: 1;
  margin: 0 10rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;

  &::after {
    border: none;
  }
}

.prev-button {
  background-color: #fff0eb;
  color: #fb6b3d;
}

.next-button {
  background-color: #fff0eb;
  color: #fb6b3d;
}

.next-button.disabled {
  background-color: #f5f5f5;
  color: #999;
  opacity: 0.7;
}

.submit-button {
  width: 320rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #fb6b3d;
  color: #fff;
  border-radius: 8rpx;
  font-size: 30rpx;
}
</style>


<style>
/* 禁用的省份选择样式 */
.disabled-province {
  cursor: not-allowed;
  opacity: 0.8;
  pointer-events: none;
}

.disabled-province.active {
  background-color: #fff0eb !important;
  color: #fb6b3d !important;
  font-weight: bold;
  border: 2rpx solid #fb6b3d;
}

.province-note {
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
  text-align: center;
  padding: 10rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}
.input-field {
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  margin-top: 20rpx;
  line-height: 80rpx;
  background-color: #fff;
}

.selected-major {
  background-color: #FFEB3B !important;
  border: 2rpx solid #FFC107 !important;
  box-shadow: 0 4rpx 12rpx rgba(255, 193, 7, 0.5) !important;
}

.selected-major-text {
  color: #FF6600 !important;
  font-weight: bold !important;
}

/* 专业项的默认样式 */
.major-item {
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  position: relative;
  background-color: #f8f8f8;
  transition: all 0.3s ease;
}

/* 选中的专业项样式 */
.major-item-selected {
  background-color: #FFEB3B !important;
  border: 2rpx solid #FFC107 !important;
  box-shadow: 0 4rpx 12rpx rgba(255, 193, 7, 0.5) !important;
}

/* 选中标记 */
.selected-tag {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: #FF6600;
  color: white;
  padding: 5rpx 10rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
}

/* 专业标题 */
.major-title {
  font-size: 28rpx;
  margin-bottom: 10rpx;
  display: flex;
  justify-content: space-between;
}

/* 已选专业展示区域样式 */
.selected-majors-container {
  background-color: #fff;
  padding: 6rpx 10rpx;
  margin: 5rpx 0 10rpx 0;
}

.selected-majors-title {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
  margin-right: 10rpx;
  white-space: nowrap;
}

.selected-majors-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.selected-major-item {
  background-color: #FFF0EB;
  border: 1rpx solid #FB6B3D;
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
  margin-right: 10rpx;
  margin-bottom: 6rpx;
  font-size: 22rpx;
  color: #FB6B3D;
  display: flex;
  align-items: center;
}

.remove-major {
  margin-left: 6rpx;
  font-size: 26rpx;
  font-weight: bold;
  color: #FB6B3D;
}

/* 选中的专业标题 */
.major-title-selected {
  color: #FB6B3D !important;
  font-weight: bold !important;
}

/* 多题目容器样式 */
.multi-questions-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.multi-question-item {
  margin-bottom: 40rpx;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 30rpx;
}

.multi-question-item:last-child {
  border-bottom: none;
}

.multi-question-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.multi-question-number {
  width: 60rpx;
  height: 60rpx;
  background-color: #FB6B3D;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 20rpx;
}

.multi-question-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  flex: 1;
}

/* 火焰特效样式 */
.flame-effect {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 60rpx;
  height: 60rpx;
  pointer-events: none;
  z-index: 5;
}

.flame {
  position: absolute;
  font-size: 24rpx;
  animation: flameAnimation 1.5s ease-in-out infinite;
}

.flame1 {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 0s;
}

.flame2 {
  top: 15rpx;
  left: 20%;
  animation-delay: 0.3s;
}

.flame3 {
  top: 15rpx;
  right: 20%;
  animation-delay: 0.6s;
}

@keyframes flameAnimation {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  25% {
    transform: translateY(-8rpx) scale(1.1);
    opacity: 0.9;
  }
  50% {
    transform: translateY(-15rpx) scale(1.2);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-8rpx) scale(1.1);
    opacity: 0.9;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* 就业方向样式 */
.career-direction {
  color: #666 !important;
  font-size: 24rpx !important;
  line-height: 1.4 !important;
  max-width: 400rpx;
  word-wrap: break-word;
}

/* 新的专业展示布局样式 */
.major-display-container {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  height: auto;
  min-height: 400rpx;
  max-height: calc(100vh - 300rpx);
  width: 100%;
  box-sizing: border-box;
}

.category-nav {
  width: 200rpx;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.nav-item {
  padding: 28rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
  border-bottom: 1rpx solid #e5e5e5;
  cursor: pointer;
}

.nav-item.active {
  background: #fff;
  color: #FF6B35;
  font-weight: bold;
  position: relative;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 40rpx;
  background: #FF6B35;
  border-radius: 3rpx 0 0 3rpx;
}

.major-list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.category-header {
  display: flex;
  align-items: center;
  padding: 15rpx 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
  background: #fff;
  height: 60rpx;
  box-sizing: border-box;
  flex-shrink: 0;
}

.category-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #FF6B35;
  margin-right: 20rpx;
}

.major-count {
  font-size: 26rpx;
  color: #999;
  flex: 1;
}

.expand-icon {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s ease;
  transform: rotate(180deg);
}

.expand-icon.expanded {
  transform: rotate(0deg);
}

.major-scroll {
  flex: 1;
  padding: 0 20rpx;
  padding-bottom: 200rpx;
  overflow-y: auto;
  box-sizing: border-box;
  min-height: 0;
  max-height: calc(100vh - 300rpx);
}

.major-item-new {
  background: #fff;
  border-radius: 8rpx;
  margin: 8rpx 0;
  padding: 16rpx;
  position: relative;
  border: 1rpx solid #f0f0f0;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.major-item-new.selected {
  border-color: #FB6B3D;
  background: #FFF0EB;
  box-shadow: 0 4rpx 12rpx rgba(251, 107, 61, 0.3);
}

.major-item-new.recommended {
  border-color: #FF6B35;
  background: linear-gradient(135deg, #fff 0%, #fff8f5 100%);
}

.flame-effect-new {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  pointer-events: none;
  z-index: 5;
}

.recommended-badge {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background: linear-gradient(45deg, #FF6B35, #FF8E53);
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: bold;
  z-index: 10;
}

.major-name-new {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.major-name-new.has-badge {
  margin-left: 70rpx;
}

.major-item-new.recommended .major-name-new {
  color: #FF6B35;
}

.major-item-new.selected .major-name-new {
  color: #FB6B3D;
}

.major-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  gap: 12rpx;
}

.detail-item {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 24rpx;
  color: #999;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 男女比例条状显示样式 */
.gender-ratio-row {
  flex-direction: column;
  align-items: stretch;
}

.gender-ratio-container {
  width: 100%;
}

.gender-ratio-bar {
  display: flex;
  height: 16rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin: 8rpx 0;
  background: #f0f0f0;
}

.male-bar {
  background: linear-gradient(90deg, #4A90E2, #5BA0F2);
  transition: width 0.3s ease;
}

.female-bar {
  background: linear-gradient(90deg, #E85D75, #F06292);
  transition: width 0.3s ease;
}

.ratio-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.male-text {
  font-size: 20rpx;
  color: #4A90E2;
  font-weight: 500;
}

.female-text {
  font-size: 20rpx;
  color: #E85D75;
  font-weight: 500;
}

/* 就业方向样式 */
.career-direction-row {
  margin-top: 8rpx;
  padding-top: 8rpx;
  border-top: 1rpx solid #f0f0f0;
}

.career-direction-text {
  font-size: 22rpx;
  color: #666;
  line-height: 1.3;
  margin-left: 8rpx;
}

.selected-indicator {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 专业选择容器高度控制 */
.major-selection-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  box-sizing: border-box;
  min-height: 0;
}

.major-selection-container .major-display-container {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

/* 专业选择题目的特殊样式 */
.question-content.major-selection-container {
  padding-bottom: 10rpx;
}
</style>